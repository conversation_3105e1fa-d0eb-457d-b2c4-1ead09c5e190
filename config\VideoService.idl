module VideoService {
    // 视频格式枚举
    enum FrameFormat {
        UNKNOWN,
        YUV420,
        NV12,
        RGB24,
        BGR24,
        H264,
        H265
    };
    
    // 优化后的视频帧结构
    struct VideoFrame {
        @key uint64_t frame_id;        // 帧ID (64位足够)
        uint64_t timestamp;             // 采集时间戳 (us)
        uint16_t width;                 // 视频宽度
        uint16_t height;                // 视频高度
        FrameFormat format;             // 视频格式
        
        // 硬件加速支持
        int32_t dma_fd;                // DMA缓冲区文件描述符
        uint32_t data_length;           // 有效数据长度
        
        // 软件后备缓冲区
        sequence<octet, 1920*1080*3> data; // 最大支持1080p BGR
        
        // 元数据
        octet source_type;              // 0=V4L2, 1=RTSP
        boolean is_keyframe;            // 关键帧标志 (编码时重要)
    };
};

