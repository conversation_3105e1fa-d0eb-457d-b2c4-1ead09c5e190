#!/bin/sh

# 监控周期（秒）
INTERVAL=5

while true; do
    # 获取CPU使用率
    CPU_USAGE=$(top -bn1 | awk '/^CPU/ {print 100-$8}')
    
    # 获取内存使用率
    MEM_USAGE=$(free | awk '/Mem:/ {print $3/$2*100}')
    
    # 动态调整策略
    if [ $(echo "$CPU_USAGE > 85" | bc) -eq 1 ]; then
        # 降分辨率
        kill -SIGUSR1 $(pidof video_capture)
        
        # 降低AI处理频率
        kill -SIGUSR1 $(pidof ai_processor)
    elif [ $(echo "$CPU_USAGE < 60" | bc) -eq 1 ]; then
        # 恢复分辨率
        kill -SIGUSR2 $(pidof video_capture)
        
        # 恢复AI处理频率
        kill -SIGUSR2 $(pidof ai_processor)
    fi
    
    # 清理DDS缓存
    if [ $(echo "$MEM_USAGE > 90" | bc) -eq 1 ]; then
        kill -SIGUSR1 $(pidof video_converter)
    fi
    
    sleep $INTERVAL
done

