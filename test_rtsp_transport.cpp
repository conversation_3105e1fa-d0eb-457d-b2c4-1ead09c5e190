// 测试RTSP传输协议选择的程序
#include "include/video_capture.h"
#include "include/capture_config.h"
#include <iostream>
#include <chrono>
#include <thread>

void test_rtsp_transport(const std::string& url, bool use_tcp) {
    std::cout << "\n=== Testing RTSP " << (use_tcp ? "TCP" : "UDP") << " Transport ===" << std::endl;
    std::cout << "URL: " << url << std::endl;
    
    RTSPClient client;
    
    auto start_time = std::chrono::high_resolution_clock::now();
    
    // 初始化RTSP客户端
    if (!client.init(url, use_tcp, 5000000)) { // 5秒超时
        std::cerr << "Failed to initialize RTSP client with " 
                  << (use_tcp ? "TCP" : "UDP") << " transport" << std::endl;
        return;
    }
    
    auto connect_time = std::chrono::high_resolution_clock::now();
    auto connect_duration = std::chrono::duration_cast<std::chrono::milliseconds>(
        connect_time - start_time).count();
    
    std::cout << "Connection established in " << connect_duration << "ms" << std::endl;
    std::cout << "Stream info: " << client.get_width() << "x" << client.get_height() << std::endl;
    
    // 测试帧获取性能
    int frame_count = 0;
    int valid_frames = 0;
    auto test_start = std::chrono::high_resolution_clock::now();
    
    std::cout << "Testing frame acquisition for 10 seconds..." << std::endl;
    
    while (frame_count < 300) { // 最多测试300帧或10秒
        auto current_time = std::chrono::high_resolution_clock::now();
        auto elapsed = std::chrono::duration_cast<std::chrono::seconds>(
            current_time - test_start).count();
        
        if (elapsed >= 10) break; // 10秒超时
        
        Frame frame = client.get_frame();
        frame_count++;
        
        if (frame.valid && !frame.data.empty()) {
            valid_frames++;
            
            // 每50帧输出一次统计
            if (valid_frames % 50 == 0) {
                auto now = std::chrono::high_resolution_clock::now();
                auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(
                    now - test_start).count();
                double fps = (double)valid_frames * 1000.0 / duration;
                std::cout << "Frames: " << valid_frames << ", FPS: " 
                          << std::fixed << std::setprecision(1) << fps 
                          << ", Data size: " << frame.data.size() << " bytes" << std::endl;
            }
        }
        
        // 短暂休眠避免CPU占用过高
        std::this_thread::sleep_for(std::chrono::milliseconds(1));
    }
    
    auto test_end = std::chrono::high_resolution_clock::now();
    auto total_duration = std::chrono::duration_cast<std::chrono::milliseconds>(
        test_end - test_start).count();
    
    // 输出测试结果
    std::cout << "\n--- Test Results ---" << std::endl;
    std::cout << "Transport: " << (use_tcp ? "TCP" : "UDP") << std::endl;
    std::cout << "Connection time: " << connect_duration << "ms" << std::endl;
    std::cout << "Test duration: " << total_duration << "ms" << std::endl;
    std::cout << "Total frames attempted: " << frame_count << std::endl;
    std::cout << "Valid frames received: " << valid_frames << std::endl;
    std::cout << "Success rate: " << std::fixed << std::setprecision(1) 
              << (double)valid_frames / frame_count * 100.0 << "%" << std::endl;
    
    if (total_duration > 0) {
        double avg_fps = (double)valid_frames * 1000.0 / total_duration;
        std::cout << "Average FPS: " << std::fixed << std::setprecision(2) << avg_fps << std::endl;
    }
    
    client.cleanup();
}

int main(int argc, char* argv[]) {
    if (argc < 2) {
        std::cout << "Usage: " << argv[0] << " <rtsp_url> [test_both]" << std::endl;
        std::cout << "Examples:" << std::endl;
        std::cout << "  " << argv[0] << " rtsp://192.168.1.100:554/stream" << std::endl;
        std::cout << "  " << argv[0] << " rtsp://admin:pass@192.168.1.100/stream test_both" << std::endl;
        return 1;
    }
    
    std::string rtsp_url = argv[1];
    bool test_both = (argc > 2 && std::string(argv[2]) == "test_both");
    
    std::cout << "RTSP Transport Protocol Test" << std::endl;
    std::cout << "============================" << std::endl;
    
    if (test_both) {
        // 测试UDP传输（默认首选）
        test_rtsp_transport(rtsp_url, false);
        
        std::cout << "\nWaiting 2 seconds before next test..." << std::endl;
        std::this_thread::sleep_for(std::chrono::seconds(2));
        
        // 测试TCP传输（备选）
        test_rtsp_transport(rtsp_url, true);
        
        std::cout << "\n=== Comparison Summary ===" << std::endl;
        std::cout << "UDP: Lower latency, higher throughput, may have packet loss" << std::endl;
        std::cout << "TCP: Higher reliability, slightly higher latency, no packet loss" << std::endl;
        std::cout << "Recommendation: Use UDP for real-time applications, TCP for reliability" << std::endl;
        
    } else {
        // 只测试UDP传输（默认）
        test_rtsp_transport(rtsp_url, false);
    }
    
    return 0;
}
