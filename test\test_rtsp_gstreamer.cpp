/**
 * @file test_rtsp_gstreamer.cpp
 * @brief GStreamer RTSP客户端测试程序
 */

#include <iostream>
#include <iomanip>
#include <chrono>
#include <thread>
#include <signal.h>
#include "../include/video_capture.h"

static bool g_running = true;

void signal_handler(int signal) {
    std::cout << "\nReceived signal " << signal << ", shutting down..." << std::endl;
    g_running = false;
}

int main(int argc, char* argv[]) {
    // 设置信号处理
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);

    std::string rtsp_url;
    bool use_tcp = false;
    bool hw_decode = true;

    // 解析命令行参数
    if (argc < 2) {
        std::cout << "Usage: " << argv[0] << " <rtsp_url> [tcp] [sw_decode]" << std::endl;
        std::cout << "Example: " << argv[0] << " rtsp://*************:554/stream tcp" << std::endl;
        return 1;
    }

    rtsp_url = argv[1];

    for (int i = 2; i < argc; i++) {
        std::string arg = argv[i];
        if (arg == "tcp") {
            use_tcp = true;
        } else if (arg == "sw_decode") {
            hw_decode = false;
        }
    }

    std::cout << "=== GStreamer RTSP Client Test ===" << std::endl;
    std::cout << "RTSP URL: " << rtsp_url << std::endl;
    std::cout << "Use TCP: " << (use_tcp ? "Yes" : "No") << std::endl;
    std::cout << "Hardware Decode: " << (hw_decode ? "Yes" : "No") << std::endl;
    std::cout << "=================================" << std::endl;

    // 创建RTSP客户端
    RTSPClient client;

    // 初始化客户端
    std::cout << "Initializing RTSP client..." << std::endl;
    if (!client.init(rtsp_url, use_tcp, hw_decode)) {
        std::cerr << "Failed to initialize RTSP client" << std::endl;
        return 1;
    }

    std::cout << "RTSP client initialized successfully" << std::endl;
    std::cout << "Starting frame capture (Press Ctrl+C to stop)..." << std::endl;

    // 统计信息
    int frame_count = 0;
    int keyframe_count = 0;
    auto start_time = std::chrono::steady_clock::now();
    auto last_stats_time = start_time;

    // 主循环
    while (g_running && client.is_connected()) {
        Frame frame = client.get_frame();
        
        if (frame.valid) {
            frame_count++;
            if (frame.is_keyframe) {
                keyframe_count++;
            }

            // 每5秒输出一次统计信息
            auto current_time = std::chrono::steady_clock::now();
            auto stats_duration = std::chrono::duration_cast<std::chrono::seconds>(
                current_time - last_stats_time).count();

            if (stats_duration >= 5) {
                auto total_duration = std::chrono::duration_cast<std::chrono::seconds>(
                    current_time - start_time).count();
                
                double fps = total_duration > 0 ? (double)frame_count / total_duration : 0.0;
                double keyframe_ratio = frame_count > 0 ? (double)keyframe_count / frame_count * 100.0 : 0.0;

                std::cout << "Stats - Total frames: " << frame_count 
                         << ", FPS: " << std::fixed << std::setprecision(2) << fps
                         << ", Keyframes: " << keyframe_count 
                         << " (" << std::setprecision(1) << keyframe_ratio << "%)"
                         << ", Resolution: " << frame.width << "x" << frame.height
                         << ", Data size: " << frame.data.size() << " bytes" << std::endl;

                last_stats_time = current_time;
            }
        } else {
            // 没有获取到有效帧，短暂休眠
            std::this_thread::sleep_for(std::chrono::milliseconds(10));
        }

        // 检查连接状态
        if (!client.is_connected()) {
            std::cerr << "RTSP connection lost" << std::endl;
            break;
        }
    }

    // 最终统计
    auto end_time = std::chrono::steady_clock::now();
    auto total_duration = std::chrono::duration_cast<std::chrono::seconds>(
        end_time - start_time).count();
    
    double avg_fps = total_duration > 0 ? (double)frame_count / total_duration : 0.0;
    double keyframe_ratio = frame_count > 0 ? (double)keyframe_count / frame_count * 100.0 : 0.0;

    std::cout << "\n=== Final Statistics ===" << std::endl;
    std::cout << "Total runtime: " << total_duration << " seconds" << std::endl;
    std::cout << "Total frames: " << frame_count << std::endl;
    std::cout << "Average FPS: " << std::fixed << std::setprecision(2) << avg_fps << std::endl;
    std::cout << "Keyframes: " << keyframe_count 
             << " (" << std::setprecision(1) << keyframe_ratio << "%)" << std::endl;
    std::cout << "========================" << std::endl;

    std::cout << "Test completed" << std::endl;
    return 0;
}
