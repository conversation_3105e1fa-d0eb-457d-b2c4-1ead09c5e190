[Unit]
Description=Video Service System
Documentation=man:video_service(8)
After=network.target sound.target
Wants=network.target

[Service]
Type=forking
User=root
Group=root
ExecStart=@DATADIR@/video_service_manager.sh start
ExecStop=@DATADIR@/video_service_manager.sh stop
ExecReload=@DATADIR@/video_service_manager.sh restart
PIDFile=/var/run/video_service/video_service.pid
Restart=on-failure
RestartSec=5
TimeoutStartSec=60
TimeoutStopSec=30

# Security settings
NoNewPrivileges=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/var/log/video_service /var/run/video_service /tmp/dds_shm
PrivateTmp=true
PrivateDevices=false
ProtectKernelTunables=true
ProtectKernelModules=true
ProtectControlGroups=true
RestrictRealtime=false
RestrictSUIDSGID=true

# Resource limits
LimitNOFILE=65536
LimitMEMLOCK=infinity
LimitCORE=0

# Environment
Environment=GST_PLUGIN_PATH=/usr/lib/gstreamer-1.0
Environment=LD_LIBRARY_PATH=/usr/local/lib:/usr/lib
Environment=DDS_DOMAIN_ID=0

[Install]
WantedBy=multi-user.target
