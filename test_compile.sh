#!/bin/bash

# 编译测试脚本 - 验证所有链接问题修复

echo "=== Video Service Compilation Test ==="
echo "Testing compilation fixes for DDS and WebRTC linking issues"
echo

# 设置颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 测试函数
test_compilation() {
    local test_name="$1"
    local source_file="$2"
    local extra_flags="$3"
    
    echo -e "${YELLOW}Testing: $test_name${NC}"
    
    # 编译命令
    g++ -std=c++17 -I./include -I./dds_video_frame \
        $(pkg-config --cflags gstreamer-1.0 gstreamer-app-1.0 gstreamer-video-1.0 gstreamer-webrtc-1.0 gstreamer-sdp-1.0) \
        $(pkg-config --cflags libavformat libavcodec libavutil) \
        -I/usr/include/fastdds -I/usr/include/fastcdr \
        $extra_flags \
        -o test_output "$source_file" \
        $(pkg-config --libs gstreamer-1.0 gstreamer-app-1.0 gstreamer-video-1.0 gstreamer-webrtc-1.0 gstreamer-sdp-1.0) \
        $(pkg-config --libs libavformat libavcodec libavutil) \
        -lfastdds -lfastcdr -pthread -ldl \
        2>&1
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✓ $test_name: PASSED${NC}"
        rm -f test_output
        return 0
    else
        echo -e "${RED}✗ $test_name: FAILED${NC}"
        return 1
    fi
}

# 创建临时目录
mkdir -p temp_test
cd temp_test

# 测试1: DDS链接测试
echo "Creating DDS link test..."
cat > test_dds.cpp << 'EOF'
#include "../include/common.h"
#include <iostream>

int main() {
    try {
        DDSVideoWriter writer("test_topic", 3);
        DDSVideoReader reader("test_topic", 3);
        std::cout << "DDS test passed" << std::endl;
        return 0;
    } catch (const std::exception& e) {
        std::cerr << "DDS test failed: " << e.what() << std::endl;
        return 1;
    }
}
EOF

# 测试2: WebRTC链接测试
echo "Creating WebRTC link test..."
cat > test_webrtc.cpp << 'EOF'
#include <gst/gst.h>
#include <gst/webrtc/webrtc.h>
#include <gst/sdp/sdp.h>
#include <iostream>

int main() {
    gst_init(nullptr, nullptr);
    
    GstSDPMessage* sdp_msg = nullptr;
    gst_sdp_message_new(&sdp_msg);
    
    if (sdp_msg) {
        GstWebRTCSessionDescription* desc = gst_webrtc_session_description_new(
            GST_WEBRTC_SDP_TYPE_OFFER, sdp_msg);
        
        if (desc) {
            gst_webrtc_session_description_free(desc);
            std::cout << "WebRTC test passed" << std::endl;
            gst_deinit();
            return 0;
        }
        gst_sdp_message_free(sdp_msg);
    }
    
    gst_deinit();
    std::cerr << "WebRTC test failed" << std::endl;
    return 1;
}
EOF

# 测试3: 完整头文件包含测试
echo "Creating header inclusion test..."
cat > test_headers.cpp << 'EOF'
#include "../include/common.h"
#include "../include/video_capture.h"
#include "../include/video_converter.h"
#include "../include/ai_processor.h"
#include "../include/cloud_streamer.h"
#include <iostream>

int main() {
    std::cout << "All headers included successfully" << std::endl;
    return 0;
}
EOF

echo
echo "=== Running Compilation Tests ==="
echo

# 运行测试
failed_tests=0

if ! test_compilation "DDS Linking" "test_dds.cpp"; then
    ((failed_tests++))
fi

echo

if ! test_compilation "WebRTC Linking" "test_webrtc.cpp"; then
    ((failed_tests++))
fi

echo

if ! test_compilation "Header Inclusion" "test_headers.cpp"; then
    ((failed_tests++))
fi

echo

# 清理
cd ..
rm -rf temp_test

# 总结
echo "=== Test Summary ==="
if [ $failed_tests -eq 0 ]; then
    echo -e "${GREEN}All tests passed! ✓${NC}"
    echo "The linking issues have been successfully resolved."
    echo
    echo "You can now compile the main services:"
    echo "  mkdir -p build && cd build"
    echo "  cmake .. && make"
    echo "  # or use: make -f ../Makefile"
    exit 0
else
    echo -e "${RED}$failed_tests test(s) failed! ✗${NC}"
    echo "Please check the error messages above and ensure all dependencies are installed."
    echo
    echo "Required packages (Ubuntu/Debian):"
    echo "  sudo apt-get install libgstreamer1.0-dev libgstreamer-plugins-base1.0-dev"
    echo "  sudo apt-get install libgstreamer-plugins-good1.0-dev libgstreamer-plugins-bad1.0-dev"
    echo "  sudo apt-get install libavformat-dev libavcodec-dev libavutil-dev"
    echo "  sudo apt-get install libfastdds-dev libfastcdr-dev"
    exit 1
fi
