@echo off
REM GStreamer RTSP测试构建脚本 (Windows)

echo === GStreamer RTSP Client Build Test ===

REM 检查是否安装了GStreamer
where gst-inspect-1.0 >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: GStreamer not found in PATH
    echo Please install GStreamer and add it to PATH
    echo Download from: https://gstreamer.freedesktop.org/download/
    exit /b 1
)

echo GStreamer found in PATH

REM 检查pkg-config
where pkg-config >nul 2>&1
if %errorlevel% neq 0 (
    echo Warning: pkg-config not found
    echo Using default GStreamer paths
    set GSTREAMER_INCLUDE=-IC:\gstreamer\1.0\msvc_x86_64\include\gstreamer-1.0 -IC:\gstreamer\1.0\msvc_x86_64\include\glib-2.0 -IC:\gstreamer\1.0\msvc_x86_64\lib\glib-2.0\include
    set GSTREAMER_LIBS=-LC:\gstreamer\1.0\msvc_x86_64\lib -lgstreamer-1.0 -lgstapp-1.0 -lgstvideo-1.0 -lgstrtsp-1.0 -lgobject-2.0 -lglib-2.0
) else (
    echo Using pkg-config for GStreamer flags
    for /f "delims=" %%i in ('pkg-config --cflags gstreamer-1.0 gstreamer-app-1.0 gstreamer-video-1.0') do set GSTREAMER_INCLUDE=%%i
    for /f "delims=" %%i in ('pkg-config --libs gstreamer-1.0 gstreamer-app-1.0 gstreamer-video-1.0') do set GSTREAMER_LIBS=%%i
)

echo GStreamer include flags: %GSTREAMER_INCLUDE%
echo GStreamer library flags: %GSTREAMER_LIBS%

REM 创建构建目录
if not exist build_test mkdir build_test

REM 检查编译器
where g++ >nul 2>&1
if %errorlevel% neq 0 (
    where cl >nul 2>&1
    if %errorlevel% neq 0 (
        echo Error: No C++ compiler found (g++ or cl)
        echo Please install MinGW-w64 or Visual Studio
        exit /b 1
    ) else (
        echo Using MSVC compiler
        set COMPILER=cl
        set COMPILE_FLAGS=/std:c++17 /EHsc /I.\include %GSTREAMER_INCLUDE%
        set OUTPUT_FLAG=/Fe:build_test\test_rtsp_gstreamer.exe
        set LINK_FLAGS=%GSTREAMER_LIBS%
    )
) else (
    echo Using GCC compiler
    set COMPILER=g++
    set COMPILE_FLAGS=-std=c++17 -Wall -Wextra -O2 -I.\include %GSTREAMER_INCLUDE%
    set OUTPUT_FLAG=-o build_test/test_rtsp_gstreamer.exe
    set LINK_FLAGS=%GSTREAMER_LIBS% -lpthread
)

echo Compiling RTSP GStreamer test...
%COMPILER% %COMPILE_FLAGS% %OUTPUT_FLAG% test\test_rtsp_gstreamer.cpp %LINK_FLAGS%

if %errorlevel% equ 0 (
    echo ✓ Compilation successful!
    echo Test executable: build_test\test_rtsp_gstreamer.exe
    echo.
    echo Usage:
    echo   build_test\test_rtsp_gstreamer.exe ^<rtsp_url^> [tcp] [sw_decode]
    echo.
    echo Examples:
    echo   build_test\test_rtsp_gstreamer.exe rtsp://192.168.1.100:554/stream
    echo   build_test\test_rtsp_gstreamer.exe rtsp://192.168.1.100:554/stream tcp
    echo   build_test\test_rtsp_gstreamer.exe rtsp://192.168.1.100:554/stream tcp sw_decode
) else (
    echo ✗ Compilation failed!
    exit /b 1
)

echo === Build Test Complete ===
pause
