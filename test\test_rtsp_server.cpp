#include "rtsp_server.h"
#include "logger.h"
#include <gtest/gtest.h>
#include <thread>
#include <chrono>

class RTSPServerTest : public ::testing::Test {
protected:
    void SetUp() override {
        // 设置测试配置
        config_.dds_topic = "TestVideoFrames";
        config_.server_address = "127.0.0.1";
        config_.server_port = 8555;  // 使用不同端口避免冲突
        config_.mount_point = "/test";
        config_.output_width = 640;
        config_.output_height = 480;
        config_.output_fps = 15;
        config_.output_bitrate = 1000000;
        config_.use_hardware_encoder = false;  // 测试时使用软件编码器
        config_.max_clients = 2;
        config_.buffer_size = 3;
        
        server_ = std::make_unique<RTSPServerService>();
    }
    
    void TearDown() override {
        if (server_) {
            server_->stop();
            server_.reset();
        }
    }
    
    RTSPServerConfig config_;
    std::unique_ptr<RTSPServerService> server_;
};

// 测试RTSP服务器初始化
TEST_F(RTSPServerTest, InitializationTest) {
    EXPECT_TRUE(server_->init(config_));
    EXPECT_FALSE(server_->is_running());
}

// 测试RTSP服务器启动和停止
TEST_F(RTSPServerTest, StartStopTest) {
    ASSERT_TRUE(server_->init(config_));
    
    // 启动服务器
    EXPECT_TRUE(server_->start());
    EXPECT_TRUE(server_->is_running());
    
    // 等待一段时间确保服务器稳定运行
    std::this_thread::sleep_for(std::chrono::seconds(1));
    EXPECT_TRUE(server_->is_running());
    
    // 停止服务器
    server_->stop();
    EXPECT_FALSE(server_->is_running());
}

// 测试配置参数验证
TEST_F(RTSPServerTest, ConfigValidationTest) {
    // 测试有效配置
    EXPECT_TRUE(server_->init(config_));
    
    // 测试无效端口
    RTSPServerConfig invalid_config = config_;
    invalid_config.server_port = -1;
    auto invalid_server = std::make_unique<RTSPServerService>();
    // 注意：这里假设init会验证配置，实际实现中可能需要添加验证逻辑
    
    // 测试无效分辨率
    invalid_config = config_;
    invalid_config.output_width = 0;
    invalid_config.output_height = 0;
}

// 测试统计信息
TEST_F(RTSPServerTest, StatisticsTest) {
    ASSERT_TRUE(server_->init(config_));
    ASSERT_TRUE(server_->start());
    
    // 获取初始统计信息
    auto stats = server_->get_stats();
    EXPECT_EQ(stats.total_connections, 0);
    EXPECT_EQ(stats.active_connections, 0);
    EXPECT_EQ(stats.error_count, 0);
    
    // 运行一段时间
    std::this_thread::sleep_for(std::chrono::seconds(2));
    
    stats = server_->get_stats();
    EXPECT_GT(stats.uptime_seconds, 1.0);
}

// 测试动态参数更新
TEST_F(RTSPServerTest, DynamicUpdateTest) {
    ASSERT_TRUE(server_->init(config_));
    
    // 测试码率更新
    EXPECT_TRUE(server_->update_bitrate(2000000));
    EXPECT_FALSE(server_->update_bitrate(-1));  // 无效码率
    
    // 测试GOP大小更新
    EXPECT_TRUE(server_->update_quality(60));
    EXPECT_FALSE(server_->update_quality(0));   // 无效GOP大小
}

// 测试VideoFormatConverter
TEST_F(RTSPServerTest, VideoConverterTest) {
    VideoFormatConverter converter;
    
    // 测试初始化
    EXPECT_TRUE(converter.init(640, 480, V4L2_PIX_FMT_YUYV, 
                              320, 240, 15, false));
    
    // 测试统计信息
    EXPECT_EQ(converter.get_frames_converted(), 0);
    EXPECT_EQ(converter.get_avg_conversion_time_ms(), 0.0);
    
    // 清理
    converter.cleanup();
}

// 测试RTSPServerUtils工具函数
TEST_F(RTSPServerTest, UtilsTest) {
    // 测试GStreamer初始化
    EXPECT_TRUE(RTSPServerUtils::init_gstreamer());
    
    // 测试格式转换
    std::string caps = RTSPServerUtils::v4l2_format_to_gst_caps(
        V4L2_PIX_FMT_YUYV, 640, 480, 30);
    EXPECT_FALSE(caps.empty());
    EXPECT_NE(caps.find("YUY2"), std::string::npos);
    
    // 测试硬件编码器支持检查
    bool hw_support = RTSPServerUtils::check_hardware_encoder_support("H264");
    // 结果取决于系统环境，只检查函数不崩溃
    (void)hw_support;
}

// 性能测试 - 测试服务器在负载下的表现
TEST_F(RTSPServerTest, PerformanceTest) {
    ASSERT_TRUE(server_->init(config_));
    ASSERT_TRUE(server_->start());
    
    auto start_time = std::chrono::high_resolution_clock::now();
    
    // 模拟运行5秒
    std::this_thread::sleep_for(std::chrono::seconds(5));
    
    auto end_time = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::seconds>(end_time - start_time);
    
    auto stats = server_->get_stats();
    EXPECT_GE(stats.uptime_seconds, 4.0);  // 至少运行4秒
    EXPECT_LT(stats.uptime_seconds, 10.0); // 不超过10秒
    
    // 打印性能统计
    server_->print_stats();
}

// 集成测试 - 测试与DDS的集成
TEST_F(RTSPServerTest, DDSIntegrationTest) {
    // 这个测试需要实际的DDS环境
    // 在CI/CD环境中可能需要跳过
    if (getenv("SKIP_DDS_TESTS")) {
        GTEST_SKIP() << "DDS integration tests skipped";
    }
    
    ASSERT_TRUE(server_->init(config_));
    ASSERT_TRUE(server_->start());
    
    // 等待DDS连接建立
    std::this_thread::sleep_for(std::chrono::seconds(2));
    
    auto stats = server_->get_stats();
    // 在没有实际DDS数据的情况下，frames_served应该为0
    EXPECT_EQ(stats.frames_served, 0);
}

// 错误处理测试
TEST_F(RTSPServerTest, ErrorHandlingTest) {
    // 测试重复初始化
    ASSERT_TRUE(server_->init(config_));
    // 第二次初始化应该成功或返回适当的状态
    
    // 测试重复启动
    ASSERT_TRUE(server_->start());
    // 第二次启动应该返回true（已经在运行）或适当处理
    bool second_start = server_->start();
    (void)second_start; // 避免未使用变量警告
    
    // 测试停止未启动的服务器
    server_->stop();
    auto stopped_server = std::make_unique<RTSPServerService>();
    stopped_server->stop(); // 应该安全地处理
}

// 配置文件测试
TEST_F(RTSPServerTest, ConfigFileTest) {
    // 创建临时配置文件
    std::string config_content = R"({
        "rtsp_server": {
            "dds_topic": "TestFrames",
            "server_port": 8556,
            "output_video": {
                "width": 800,
                "height": 600,
                "fps": 25
            }
        }
    })";
    
    // 写入临时文件
    std::ofstream temp_file("test_config.json");
    temp_file << config_content;
    temp_file.close();
    
    // 测试配置加载（如果支持JSON）
    RTSPServerConfig loaded_config;
    bool load_result = load_config_from_json("test_config.json", loaded_config);
    
#ifdef HAVE_JSONCPP
    EXPECT_TRUE(load_result);
    EXPECT_EQ(loaded_config.dds_topic, "TestFrames");
    EXPECT_EQ(loaded_config.server_port, 8556);
#else
    EXPECT_FALSE(load_result);
#endif
    
    // 清理临时文件
    std::remove("test_config.json");
}

int main(int argc, char** argv) {
    // 初始化日志
    Logger::set_level(LOG_INFO);
    
    // 初始化Google Test
    ::testing::InitGoogleTest(&argc, argv);
    
    // 运行测试
    return RUN_ALL_TESTS();
}
