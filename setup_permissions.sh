#!/bin/bash

# Setup script permissions and final system configuration

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

echo "Setting up Video Service System..."
echo "================================="

# Make all scripts executable
echo "Setting script permissions..."
chmod +x "${SCRIPT_DIR}/video_service_manager.sh"
chmod +x "${SCRIPT_DIR}/monitor_video_service.sh"
chmod +x "${SCRIPT_DIR}/test_video_service.sh"
chmod +x "${SCRIPT_DIR}/debug_video_service.sh"
chmod +x "${SCRIPT_DIR}/setup_permissions.sh"

# Create necessary directories
echo "Creating system directories..."
sudo mkdir -p /var/log/video_service
sudo mkdir -p /var/run/video_service
sudo mkdir -p /tmp/dds_shm

# Set proper ownership and permissions
sudo chown root:root /var/log/video_service
sudo chown root:root /var/run/video_service
sudo chmod 755 /var/log/video_service
sudo chmod 755 /var/run/video_service

# Setup logrotate for video service logs
echo "Setting up log rotation..."
sudo tee /etc/logrotate.d/video_service > /dev/null << 'EOF'
/var/log/video_service/*.log {
    daily
    missingok
    rotate 7
    compress
    delaycompress
    notifempty
    create 644 root root
    postrotate
        /bin/kill -HUP `cat /var/run/video_service/*.pid 2> /dev/null` 2> /dev/null || true
    endscript
}
EOF

# Create udev rules for video devices (if needed)
echo "Setting up udev rules..."
sudo tee /etc/udev/rules.d/99-video-service.rules > /dev/null << 'EOF'
# Video Service udev rules
# Ensure video devices have proper permissions
SUBSYSTEM=="video4linux", GROUP="video", MODE="0664"
SUBSYSTEM=="media", GROUP="video", MODE="0664"

# DMA buffer devices
KERNEL=="dma_heap", GROUP="video", MODE="0664"
EOF

# Reload udev rules
sudo udevadm control --reload-rules
sudo udevadm trigger

# Add current user to video group
echo "Adding user to video group..."
sudo usermod -a -G video $USER

# Create sample configuration if it doesn't exist
if [[ ! -f "${SCRIPT_DIR}/config.json" ]]; then
    echo "Creating sample configuration..."
    cp "${SCRIPT_DIR}/config.json" "${SCRIPT_DIR}/config.json.sample"
fi

# Setup systemd service if systemd is available
if systemctl --version >/dev/null 2>&1; then
    echo "Setting up systemd service..."
    
    # Generate service file from template
    sed "s|@BINDIR@|${SCRIPT_DIR}|g; s|@DATADIR@|${SCRIPT_DIR}|g" \
        "${SCRIPT_DIR}/video_service.service.in" > /tmp/video_service.service
    
    sudo cp /tmp/video_service.service /etc/systemd/system/
    sudo systemctl daemon-reload
    
    echo "Systemd service installed. Enable with:"
    echo "  sudo systemctl enable video_service"
    echo "  sudo systemctl start video_service"
fi

# Create desktop entry for GUI environments
if [[ -d "$HOME/.local/share/applications" ]]; then
    echo "Creating desktop entry..."
    mkdir -p "$HOME/.local/share/applications"
    
    cat > "$HOME/.local/share/applications/video-service.desktop" << EOF
[Desktop Entry]
Name=Video Service
Comment=Embedded Linux Video Service System
Exec=${SCRIPT_DIR}/video_service_manager.sh start
Icon=camera-video
Terminal=true
Type=Application
Categories=AudioVideo;Video;
EOF
fi

echo
echo "Setup completed successfully!"
echo "============================"
echo
echo "Next steps:"
echo "1. Review and edit config.json for your environment"
echo "2. Test the system: ./test_video_service.sh"
echo "3. Start services: sudo ./video_service_manager.sh start"
echo "4. Monitor system: ./monitor_video_service.sh report"
echo
echo "For debugging: ./debug_video_service.sh"
echo "For help: ./video_service_manager.sh"
echo
echo "Note: You may need to log out and back in for group changes to take effect."
