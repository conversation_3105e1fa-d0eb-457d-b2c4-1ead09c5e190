# Video Service Makefile
# Alternative build system for environments without CMake

# Compiler and flags
CXX = g++
CXXFLAGS = -std=c++17 -Wall -Wextra -Wpedantic
LDFLAGS = -pthread -ldl

# Build type
BUILD_TYPE ?= Release

ifeq ($(BUILD_TYPE), Debug)
    CXXFLAGS += -g -O0 -DDEBUG
else
    CXXFLAGS += -O3 -DNDEBUG
endif

# Package config
PKG_CONFIG = pkg-config

# FFmpeg
FFMPEG_CFLAGS = $(shell $(PKG_CONFIG) --cflags libavcodec libavformat libavutil libswscale libswresample)
FFMPEG_LIBS = $(shell $(PKG_CONFIG) --libs libavcodec libavformat libavutil libswscale libswresample)

# GStreamer
GSTREAMER_CFLAGS = $(shell $(PKG_CONFIG) --cflags gstreamer-1.0 gstreamer-app-1.0 gstreamer-video-1.0 gstreamer-webrtc-1.0)
GSTREAMER_LIBS = $(shell $(PKG_CONFIG) --libs gstreamer-1.0 gstreamer-app-1.0 gstreamer-video-1.0 gstreamer-webrtc-1.0)

# Fast DDS
FASTDDS_CFLAGS = -I/usr/include/fastdds -I/usr/include/fastcdr
FASTDDS_LIBS = -lfastrtps -lfastcdr

# Optional: OpenCV
OPENCV_AVAILABLE = $(shell $(PKG_CONFIG) --exists opencv4 && echo "yes" || echo "no")
ifeq ($(OPENCV_AVAILABLE), yes)
    OPENCV_CFLAGS = $(shell $(PKG_CONFIG) --cflags opencv4) -DHAVE_OPENCV
    OPENCV_LIBS = $(shell $(PKG_CONFIG) --libs opencv4)
else
    OPENCV_CFLAGS =
    OPENCV_LIBS =
endif

# Optional: TensorRT
TENSORRT_ROOT ?= /usr/local/tensorrt
TENSORRT_AVAILABLE = $(shell test -f $(TENSORRT_ROOT)/include/NvInfer.h && echo "yes" || echo "no")
ifeq ($(TENSORRT_AVAILABLE), yes)
    TENSORRT_CFLAGS = -I$(TENSORRT_ROOT)/include -DHAVE_TENSORRT
    TENSORRT_LIBS = -L$(TENSORRT_ROOT)/lib -lnvinfer -lnvonnxparser
else
    TENSORRT_CFLAGS =
    TENSORRT_LIBS =
endif

# Optional: ONNX Runtime
ONNXRUNTIME_ROOT ?= /usr/local/onnxruntime
ONNXRUNTIME_AVAILABLE = $(shell test -f $(ONNXRUNTIME_ROOT)/include/onnxruntime_cxx_api.h && echo "yes" || echo "no")
ifeq ($(ONNXRUNTIME_AVAILABLE), yes)
    ONNXRUNTIME_CFLAGS = -I$(ONNXRUNTIME_ROOT)/include -DHAVE_ONNXRUNTIME
    ONNXRUNTIME_LIBS = -L$(ONNXRUNTIME_ROOT)/lib -lonnxruntime
else
    ONNXRUNTIME_CFLAGS =
    ONNXRUNTIME_LIBS =
endif

# Combine all flags
ALL_CFLAGS = $(CXXFLAGS) $(FFMPEG_CFLAGS) $(GSTREAMER_CFLAGS) $(FASTDDS_CFLAGS) \
             $(OPENCV_CFLAGS) $(TENSORRT_CFLAGS) $(ONNXRUNTIME_CFLAGS)

ALL_LIBS = $(LDFLAGS) $(FFMPEG_LIBS) $(GSTREAMER_LIBS) $(FASTDDS_LIBS) \
           $(OPENCV_LIBS) $(TENSORRT_LIBS) $(ONNXRUNTIME_LIBS)

# Source files
SOURCES = video_capture_main.cpp video_converter_main.cpp ai_processor_main.cpp cloud_streamer_main.cpp

# Target executables
TARGETS = video_capture_main video_converter_main ai_processor_main cloud_streamer_main

# Build directory
BUILD_DIR = build
OBJ_DIR = $(BUILD_DIR)/obj

# Installation directories
PREFIX ?= /usr/local
BINDIR = $(PREFIX)/bin
DATADIR = $(PREFIX)/share/video_service
SYSTEMDDIR = /lib/systemd/system

# Default target
all: $(TARGETS)

# Create build directory
$(BUILD_DIR):
	mkdir -p $(BUILD_DIR)
	mkdir -p $(OBJ_DIR)

# Build targets
video_capture_main: video_capture_main.cpp | $(BUILD_DIR)
	$(CXX) $(ALL_CFLAGS) -o $(BUILD_DIR)/$@ $< $(ALL_LIBS)

video_converter_main: video_converter_main.cpp | $(BUILD_DIR)
	$(CXX) $(ALL_CFLAGS) -o $(BUILD_DIR)/$@ $< $(ALL_LIBS)

ai_processor_main: ai_processor_main.cpp | $(BUILD_DIR)
	$(CXX) $(ALL_CFLAGS) -o $(BUILD_DIR)/$@ $< $(ALL_LIBS)

cloud_streamer_main: cloud_streamer_main.cpp | $(BUILD_DIR)
	$(CXX) $(ALL_CFLAGS) -o $(BUILD_DIR)/$@ $< $(ALL_LIBS)

# Install targets
install: all
	install -d $(DESTDIR)$(BINDIR)
	install -d $(DESTDIR)$(DATADIR)
	
	# Install binaries
	install -m 755 $(BUILD_DIR)/video_capture_main $(DESTDIR)$(BINDIR)/
	install -m 755 $(BUILD_DIR)/video_converter_main $(DESTDIR)$(BINDIR)/
	install -m 755 $(BUILD_DIR)/ai_processor_main $(DESTDIR)$(BINDIR)/
	install -m 755 $(BUILD_DIR)/cloud_streamer_main $(DESTDIR)$(BINDIR)/
	
	# Install configuration files
	install -m 644 config.json $(DESTDIR)$(DATADIR)/
	install -m 755 video_service_manager.sh $(DESTDIR)$(DATADIR)/
	
	# Install systemd service file (if systemd is available)
	if [ -d "$(DESTDIR)$(SYSTEMDDIR)" ]; then \
		sed 's|@BINDIR@|$(BINDIR)|g; s|@DATADIR@|$(DATADIR)|g' video_service.service.in > video_service.service; \
		install -m 644 video_service.service $(DESTDIR)$(SYSTEMDDIR)/; \
	fi

# Uninstall targets
uninstall:
	rm -f $(DESTDIR)$(BINDIR)/video_capture_main
	rm -f $(DESTDIR)$(BINDIR)/video_converter_main
	rm -f $(DESTDIR)$(BINDIR)/ai_processor_main
	rm -f $(DESTDIR)$(BINDIR)/cloud_streamer_main
	rm -rf $(DESTDIR)$(DATADIR)
	rm -f $(DESTDIR)$(SYSTEMDDIR)/video_service.service

# Clean build files
clean:
	rm -rf $(BUILD_DIR)
	rm -f video_service.service

# Create distribution package
dist: clean
	tar -czf video_service-1.0.0.tar.gz \
		*.h *.cpp *.json *.sh *.in Makefile CMakeLists.txt README.md

# Development targets
debug:
	$(MAKE) BUILD_TYPE=Debug

release:
	$(MAKE) BUILD_TYPE=Release

# Check dependencies
check-deps:
	@echo "Checking dependencies..."
	@echo -n "FFmpeg: "
	@$(PKG_CONFIG) --exists libavcodec libavformat libavutil libswscale libswresample && echo "OK" || echo "MISSING"
	@echo -n "GStreamer: "
	@$(PKG_CONFIG) --exists gstreamer-1.0 gstreamer-app-1.0 gstreamer-video-1.0 && echo "OK" || echo "MISSING"
	@echo -n "Fast DDS: "
	@test -f /usr/include/fastdds/dds/DomainParticipant.hpp && echo "OK" || echo "MISSING"
	@echo -n "OpenCV: "
	@$(PKG_CONFIG) --exists opencv4 && echo "OK" || echo "OPTIONAL"
	@echo -n "TensorRT: "
	@test -f $(TENSORRT_ROOT)/include/NvInfer.h && echo "OK" || echo "OPTIONAL"
	@echo -n "ONNX Runtime: "
	@test -f $(ONNXRUNTIME_ROOT)/include/onnxruntime_cxx_api.h && echo "OK" || echo "OPTIONAL"

# Show configuration
config:
	@echo "Video Service Build Configuration:"
	@echo "  Build type: $(BUILD_TYPE)"
	@echo "  Compiler: $(CXX)"
	@echo "  C++ flags: $(CXXFLAGS)"
	@echo "  FFmpeg: $(if $(FFMPEG_LIBS),enabled,disabled)"
	@echo "  GStreamer: $(if $(GSTREAMER_LIBS),enabled,disabled)"
	@echo "  OpenCV: $(OPENCV_AVAILABLE)"
	@echo "  TensorRT: $(TENSORRT_AVAILABLE)"
	@echo "  ONNX Runtime: $(ONNXRUNTIME_AVAILABLE)"
	@echo "  Install prefix: $(PREFIX)"

# Help target
help:
	@echo "Video Service Build System"
	@echo ""
	@echo "Targets:"
	@echo "  all          - Build all executables (default)"
	@echo "  debug        - Build with debug flags"
	@echo "  release      - Build with release flags"
	@echo "  install      - Install to system"
	@echo "  uninstall    - Remove from system"
	@echo "  clean        - Remove build files"
	@echo "  dist         - Create distribution package"
	@echo "  check-deps   - Check for required dependencies"
	@echo "  config       - Show build configuration"
	@echo "  help         - Show this help"
	@echo ""
	@echo "Variables:"
	@echo "  BUILD_TYPE   - Debug or Release (default: Release)"
	@echo "  PREFIX       - Installation prefix (default: /usr/local)"
	@echo "  TENSORRT_ROOT - TensorRT installation path"
	@echo "  ONNXRUNTIME_ROOT - ONNX Runtime installation path"

.PHONY: all install uninstall clean dist debug release check-deps config help
