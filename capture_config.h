
#ifndef CAPTURE_CONFIG_H
#define CAPTURE_CONFIG_H

#include <string>

enum VideoSource {
    V4L2_SOURCE,
    RTSP_SOURCE
};

enum FrameFormat {
    UNKNOWN,
    YUV420,
    NV12,
    RGB24,
    BGR24,
    H264,
    H265
};

struct CaptureConfig {
    VideoSource source_type;

    // V4L2 source config
    std::string device = "/dev/video0";
    int width = 1280;
    int height = 720;
    int fps = 30;
    FrameFormat format = YUV420;
    bool use_dma = true;

    // RTSP source config
    std::string url;
    bool use_tcp = false;
    int timeout_ms = 5000;
    bool hw_decode = true;

    // Common config
    int buffer_count = 4;
    bool enable_timestamp = true;
};

struct StreamConfig {
    enum Type { WEBRTC, RTMP } type = WEBRTC;
    std::string url;
    int bitrate = 2000000;  // 2Mbps
    int gop_size = 30;
    bool use_hw_encoder = true;
};

struct AIConfig {
    std::string model_path;
    std::string engine_type = "tensorrt";  // tensorrt, onnx, openvino
    int batch_size = 1;
    bool use_gpu = true;
    float confidence_threshold = 0.5f;
    int max_detections = 100;
};

#endif // CAPTURE_CONFIG_H

