
#ifndef CAPTURE_CONFIG_H
#define CAPTURE_CONFIG_H

#include <string>

enum VideoSource {
    V4L2_SOURCE,
    RTSP_SOURCE
};

struct CaptureConfig {
    VideoSource source_type;

    // V4L2 source config
    std::string device = "/dev/video0";
    int width = 1280;
    int height = 720;
    int fps = 30;
    int format = V4L2_PIX_FMT_YUYV;
    bool use_dma = true;

    // RTSP source config
    std::string url;
    bool use_tcp = false;
    int timeout_us = 1000000;

    // Common config
    int buffer_count = 4;
    bool enable_timestamp = true;
};

struct StreamConfig {
    enum Type { WEBRTC, RTMP } type = WEBRTC;
    std::string url;
    int bitrate = 2000000;  // 2Mbps
    int gop_size = 15;
    bool use_hw_encoder = true;
};

struct AIConfig {
    std::string model_path;
    std::string engine_type = "tensorrt";  // tensorrt, onnx, openvino
    int batch_size = 1;
    bool use_gpu = true;
    float confidence_threshold = 0.5f;
    int max_detections = 100;
};

struct RTSPServerConfig {
    std::string dds_topic = "Video_Frames";    // DDS输入topic名称
    std::string server_address = "0.0.0.0";   // RTSP服务器绑定地址
    int server_port = 8554;                    // RTSP服务器端口
    std::string mount_point = "/stream";       // RTSP挂载点

    // 输出视频参数 (统一转换目标)
    int output_width = 1280;
    int output_height = 720;
    int output_fps = 30;
    std::string output_codec = "H264";         // H264, H265
    int output_bitrate = 2000000;              // 2Mbps
    int gop_size = 30;                         // GOP大小

    // 性能优化参数
    bool use_hardware_encoder = true;          // 硬件编码器
    bool zero_copy_mode = true;                // 零拷贝模式
    int buffer_size = 5;                       // DDS缓冲区大小
    int max_clients = 10;                      // 最大客户端数

    // 质量控制
    bool adaptive_bitrate = true;              // 自适应码率
    int min_bitrate = 500000;                  // 最小码率 500Kbps
    int max_bitrate = 5000000;                 // 最大码率 5Mbps
};

#endif // CAPTURE_CONFIG_H

