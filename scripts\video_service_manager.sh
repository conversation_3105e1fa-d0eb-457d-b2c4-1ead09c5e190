#!/bin/bash

# Video Service Manager Script
# This script manages all video service components

set -e

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "${SCRIPT_DIR}/.." && pwd)"
CONFIG_FILE="${PROJECT_ROOT}/config/config.json"
LOG_DIR="/var/log/video_service"
PID_DIR="/var/run/video_service"

# Service binaries (check both build directory and installed location)
if [[ -f "${PROJECT_ROOT}/build/video_capture_main" ]]; then
    # Development build
    VIDEO_CAPTURE_BIN="${PROJECT_ROOT}/build/video_capture_main"
    VIDEO_CONVERTER_BIN="${PROJECT_ROOT}/build/video_converter_main"
    AI_PROCESSOR_BIN="${PROJECT_ROOT}/build/ai_processor_main"
    CLOUD_STREAMER_BIN="${PROJECT_ROOT}/build/cloud_streamer_main"
else
    # Installed binaries
    VIDEO_CAPTURE_BIN="$(which video_capture_main || echo "${PROJECT_ROOT}/video_capture_main")"
    VIDEO_CONVERTER_BIN="$(which video_converter_main || echo "${PROJECT_ROOT}/video_converter_main")"
    AI_PROCESSOR_BIN="$(which ai_processor_main || echo "${PROJECT_ROOT}/ai_processor_main")"
    CLOUD_STREAMER_BIN="$(which cloud_streamer_main || echo "${PROJECT_ROOT}/cloud_streamer_main")"
fi

# Service names
SERVICES=("video_capture" "video_converter" "ai_processor" "cloud_streamer")

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" >&2
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Check if running as root
check_root() {
    if [[ $EUID -ne 0 ]]; then
        error "This script must be run as root for proper resource management"
        exit 1
    fi
}

# Create necessary directories
setup_directories() {
    log "Setting up directories..."
    mkdir -p "$LOG_DIR"
    mkdir -p "$PID_DIR"
    
    # Set permissions
    chmod 755 "$LOG_DIR"
    chmod 755 "$PID_DIR"
}

# Setup system resources
setup_system() {
    log "Setting up system resources..."
    
    # Configure shared memory for DDS
    if ! mountpoint -q /tmp/dds_shm; then
        mount -t tmpfs -o size=64m tmpfs /tmp/dds_shm
        mkdir -p /tmp/dds_shm/video_service
    fi
    
    # Configure DMA buffer pools (if supported)
    if [[ -f /sys/module/videobuf2_core/parameters/capture_pool ]]; then
        echo "8x1920x1080" > /sys/module/videobuf2_core/parameters/capture_pool
        echo "8x1920x1080" > /sys/module/videobuf2_core/parameters/convert_pool
        echo "4x1920x1080" > /sys/module/videobuf2_core/parameters/cloud_pool
    fi
    
    # Set system limits
    ulimit -n 65536  # File descriptors
    ulimit -l unlimited  # Memory lock
}

# Check if service is running
is_service_running() {
    local service_name=$1
    local pid_file="${PID_DIR}/${service_name}.pid"
    
    if [[ -f "$pid_file" ]]; then
        local pid=$(cat "$pid_file")
        if kill -0 "$pid" 2>/dev/null; then
            return 0
        else
            rm -f "$pid_file"
            return 1
        fi
    fi
    return 1
}

# Start a service
start_service() {
    local service_name=$1
    local binary_path=$2
    local args=$3
    local cpu_affinity=$4
    
    if is_service_running "$service_name"; then
        warning "$service_name is already running"
        return 0
    fi
    
    log "Starting $service_name..."
    
    # Start the service with CPU affinity
    if [[ -n "$cpu_affinity" ]]; then
        nohup taskset "$cpu_affinity" "$binary_path" $args > "${LOG_DIR}/${service_name}.log" 2>&1 &
    else
        nohup "$binary_path" $args > "${LOG_DIR}/${service_name}.log" 2>&1 &
    fi
    
    local pid=$!
    
    # Save PID
    echo "$pid" > "${PID_DIR}/${service_name}.pid"
    
    # Wait a moment and check if it's still running
    sleep 2
    if kill -0 "$pid" 2>/dev/null; then
        success "$service_name started successfully (PID: $pid)"
        return 0
    else
        error "Failed to start $service_name"
        rm -f "${PID_DIR}/${service_name}.pid"
        return 1
    fi
}

# Stop a service
stop_service() {
    local service_name=$1
    local pid_file="${PID_DIR}/${service_name}.pid"
    
    if [[ -f "$pid_file" ]]; then
        local pid=$(cat "$pid_file")
        if kill -0 "$pid" 2>/dev/null; then
            log "Stopping $service_name (PID: $pid)..."
            kill -TERM "$pid"
            
            # Wait for graceful shutdown
            local count=0
            while kill -0 "$pid" 2>/dev/null && [[ $count -lt 10 ]]; do
                sleep 1
                ((count++))
            done
            
            # Force kill if still running
            if kill -0 "$pid" 2>/dev/null; then
                warning "Force killing $service_name"
                kill -KILL "$pid"
            fi
            
            success "$service_name stopped"
        fi
        rm -f "$pid_file"
    else
        warning "$service_name is not running"
    fi
}

# Start all services
start_all() {
    log "Starting video service components..."
    
    # Start services in order with CPU affinity
    start_service "video_capture" "$VIDEO_CAPTURE_BIN" \
        "--source v4l2 --device /dev/video0 --width 1280 --height 720 --fps 30" \
        "0x03"  # CPU 0-1
    sleep 3
    
    start_service "video_converter" "$VIDEO_CONVERTER_BIN" \
        "--verbose" \
        "0x0C"  # CPU 2-3
    sleep 2
    
    start_service "ai_processor" "$AI_PROCESSOR_BIN" \
        "--engine onnx --model /opt/models/yolov5s.onnx --confidence 0.5" \
        "0x30"  # CPU 4-5
    sleep 2
    
    start_service "cloud_streamer" "$CLOUD_STREAMER_BIN" \
        "--type rtmp --url rtmp://live.example.com/live/stream_key" \
        "0xC0"  # CPU 6-7
    
    success "All services started"
}

# Stop all services
stop_all() {
    log "Stopping video service components..."
    
    # Stop services in reverse order
    for ((i=${#SERVICES[@]}-1; i>=0; i--)); do
        stop_service "${SERVICES[i]}"
    done
    
    success "All services stopped"
}

# Show status of all services
status() {
    log "Video Service Status:"
    echo
    
    for service in "${SERVICES[@]}"; do
        if is_service_running "$service"; then
            local pid=$(cat "${PID_DIR}/${service}.pid")
            local cpu_usage=$(ps -p "$pid" -o %cpu --no-headers 2>/dev/null || echo "N/A")
            local mem_usage=$(ps -p "$pid" -o %mem --no-headers 2>/dev/null || echo "N/A")
            echo -e "  ${GREEN}●${NC} $service (PID: $pid, CPU: ${cpu_usage}%, MEM: ${mem_usage}%)"
        else
            echo -e "  ${RED}●${NC} $service (stopped)"
        fi
    done
    echo
}

# Show logs
show_logs() {
    local service_name=$1
    local log_file="${LOG_DIR}/${service_name}.log"
    
    if [[ -f "$log_file" ]]; then
        tail -f "$log_file"
    else
        error "Log file not found: $log_file"
    fi
}

# Main function
main() {
    case "${1:-}" in
        start)
            check_root
            setup_directories
            setup_system
            start_all
            ;;
        stop)
            check_root
            stop_all
            ;;
        restart)
            check_root
            stop_all
            sleep 2
            setup_directories
            setup_system
            start_all
            ;;
        status)
            status
            ;;
        logs)
            if [[ -n "${2:-}" ]]; then
                show_logs "$2"
            else
                error "Please specify service name: ${SERVICES[*]}"
                exit 1
            fi
            ;;
        *)
            echo "Usage: $0 {start|stop|restart|status|logs <service_name>}"
            echo "Services: ${SERVICES[*]}"
            exit 1
            ;;
    esac
}

main "$@"
