
class CloudStreamer {
public:
    enum StreamType { WEBRTC, RTMP };
    
    void init(StreamType type, const std::string& url) {
        // 创建DDS Reader
        input_reader = create_reader("Cloud_Frames");
        
        // 初始化GStreamer管道
        if (type == WEBRTC) {
            init_webrtc_pipeline(url);
        } else {
            init_rtmp_pipeline(url);
        }
    }

    void run() {
        while (!stop_requested) {
            VideoFrame frame;
            if (input_reader->take_next_sample(&frame)) {
                push_frame(frame);
            }
        }
    }

private:
    void push_frame(const VideoFrame& frame) {
        GstBuffer* gst_buf = nullptr;
        
        // 使用DMA缓冲区
        if (frame.dma_fd > 0) {
            gst_buf = gst_dmabuf_import(frame.dma_fd, frame.data_length);
        } 
        // 使用软件缓冲区
        else {
            gst_buf = gst_buffer_new_allocate(nullptr, frame.data.size(), nullptr);
            GstMapInfo map;
            gst_buffer_map(gst_buf, &map, GST_MAP_WRITE);
            memcpy(map.data, frame.data.data(), frame.data.size());
            gst_buffer_unmap(gst_buf, &map);
        }
        
        // 设置时间戳
        GST_BUFFER_PTS(gst_buf) = frame.timestamp * 1000; // us -> ns
        
        // 推送到GStreamer
        gst_app_src_push_buffer(appsrc, gst_buf);
        
        // 释放资源
        if (frame.dma_fd > 0) {
            close(frame.dma_fd);
        }
    }
    
    void init_webrtc_pipeline(const std::string& url) {
        // 创建WebRTC管道
        pipeline = gst_parse_launch(
            "appsrc name=source ! queue ! h264parse ! rtph264pay ! "
            "webrtcbin bundle-policy=max-bundle name=webrtcbin "
            "stun-server=stun://stun.l.google.com:19302",
            nullptr);
        
        // 获取appsrc元素
        appsrc = gst_bin_get_by_name(GST_BIN(pipeline), "source");
        
        // 配置caps
        GstCaps* caps = gst_caps_new_simple("video/x-h264",
            "width", G_TYPE_INT, 1280,
            "height", G_TYPE_INT, 720,
            "framerate", GST_TYPE_FRACTION, 30, 1,
            nullptr);
        g_object_set(appsrc, "caps", caps, nullptr);
        gst_caps_unref(caps);
        
        // 设置WebRTC信号处理
        GstElement* webrtc = gst_bin_get_by_name(GST_BIN(pipeline), "webrtcbin");
        g_signal_connect(webrtc, "on-negotiation-needed", ...);
        g_signal_connect(webrtc, "on-ice-candidate", ...);
        
        // 启动管道
        gst_element_set_state(pipeline, GST_STATE_PLAYING);
    }
    
    void init_rtmp_pipeline(const std::string& url) {
        // 创建RTMP管道
        pipeline = gst_parse_launch(
            "appsrc name=source ! queue ! h264parse ! flvmux ! "
            "rtmpsink location=" + url + " live=true",
            nullptr);
        
        // 获取appsrc元素
        appsrc = gst_bin_get_by_name(GST_BIN(pipeline), "source");
        
        // 配置caps (同上)
        // ...
        
        // 启动管道
        gst_element_set_state(pipeline, GST_STATE_PLAYING);
    }
};
