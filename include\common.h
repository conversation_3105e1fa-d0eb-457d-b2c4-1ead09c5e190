#ifndef COMMON_H
#define COMMON_H

#include <memory>
#include <chrono>
#include <vector>
#include <string>
#include <atomic>
#include <thread>
#include <mutex>
#include <condition_variable>
#include <queue>
#include <functional>

// Linux系统头文件
#include <sys/time.h>
#include <sys/mman.h>
#include <sys/ioctl.h>
#include <fcntl.h>
#include <unistd.h>
#include <errno.h>
#include <string.h>

// V4L2头文件
#include <linux/videodev2.h>

// FFmpeg头文件 (用于RTSP包获取，不解码)
extern "C" {
#include <libavformat/avformat.h>
#include <libavcodec/avcodec.h>
#include <libavutil/avutil.h>
#include <libavutil/imgutils.h>
}

// GStreamer头文件 (用于video converter中的编解码)
#include <gst/gst.h>
#include <gst/app/gstappsrc.h>
#include <gst/app/gstappsink.h>
#include <gst/video/video.h>

#include "capture_config.h"

// 时间戳工具
inline uint64_t get_current_us() {
    struct timeval tv;
    gettimeofday(&tv, nullptr);
    return tv.tv_sec * 1000000ULL + tv.tv_usec;
}

inline uint64_t get_current_ns() {
    struct timespec ts;
    clock_gettime(CLOCK_MONOTONIC, &ts);
    return ts.tv_sec * 1000000000ULL + ts.tv_nsec;
}

// 视频帧结构
struct Frame {
    uint64_t frame_id = 0;
    uint64_t timestamp = 0;
    uint16_t width = 0;
    uint16_t height = 0;
    FrameFormat format = UNKNOWN;
    
    // 硬件加速支持
    int dma_fd = -1;
    uint32_t data_length = 0;
    
    // 软件缓冲区
    std::vector<uint8_t> data;
    
    // 元数据
    uint8_t source_type = 0;  // 0=V4L2, 1=RTSP
    bool is_keyframe = false;
    bool valid = false;
    
    // 构造函数
    Frame() = default;
    
    // 移动构造函数
    Frame(Frame&& other) noexcept 
        : frame_id(other.frame_id)
        , timestamp(other.timestamp)
        , width(other.width)
        , height(other.height)
        , format(other.format)
        , dma_fd(other.dma_fd)
        , data_length(other.data_length)
        , data(std::move(other.data))
        , source_type(other.source_type)
        , is_keyframe(other.is_keyframe)
        , valid(other.valid) {
        other.dma_fd = -1;
        other.valid = false;
    }
    
    // 移动赋值操作符
    Frame& operator=(Frame&& other) noexcept {
        if (this != &other) {
            // 清理当前资源
            if (dma_fd >= 0) {
                close(dma_fd);
            }
            
            // 移动数据
            frame_id = other.frame_id;
            timestamp = other.timestamp;
            width = other.width;
            height = other.height;
            format = other.format;
            dma_fd = other.dma_fd;
            data_length = other.data_length;
            data = std::move(other.data);
            source_type = other.source_type;
            is_keyframe = other.is_keyframe;
            valid = other.valid;
            
            // 清理源对象
            other.dma_fd = -1;
            other.valid = false;
        }
        return *this;
    }
    
    // 禁用拷贝
    Frame(const Frame&) = delete;
    Frame& operator=(const Frame&) = delete;
    
    // 析构函数
    ~Frame() {
        if (dma_fd >= 0) {
            close(dma_fd);
        }
    }
    
    // 获取数据大小
    size_t get_data_size() const {
        if (dma_fd >= 0) {
            return data_length;
        }
        return data.size();
    }
    
    // 获取数据指针
    const uint8_t* get_data_ptr() const {
        if (dma_fd >= 0) {
            // DMA缓冲区需要映射
            return nullptr;  // 需要特殊处理
        }
        return data.data();
    }
};

// 线程安全的帧队列
template<typename T>
class ThreadSafeQueue {
private:
    mutable std::mutex mutex_;
    std::queue<T> queue_;
    std::condition_variable condition_;
    size_t max_size_;

public:
    ThreadSafeQueue(size_t max_size = 10) : max_size_(max_size) {}
    
    void push(T item) {
        std::lock_guard<std::mutex> lock(mutex_);
        if (queue_.size() >= max_size_) {
            queue_.pop();  // 丢弃最老的帧
        }
        queue_.push(std::move(item));
        condition_.notify_one();
    }
    
    bool try_pop(T& item) {
        std::lock_guard<std::mutex> lock(mutex_);
        if (queue_.empty()) {
            return false;
        }
        item = std::move(queue_.front());
        queue_.pop();
        return true;
    }
    
    bool wait_and_pop(T& item, int timeout_ms = -1) {
        std::unique_lock<std::mutex> lock(mutex_);
        if (timeout_ms < 0) {
            condition_.wait(lock, [this] { return !queue_.empty(); });
        } else {
            if (!condition_.wait_for(lock, std::chrono::milliseconds(timeout_ms),
                                   [this] { return !queue_.empty(); })) {
                return false;
            }
        }
        item = std::move(queue_.front());
        queue_.pop();
        return true;
    }
    
    size_t size() const {
        std::lock_guard<std::mutex> lock(mutex_);
        return queue_.size();
    }
    
    bool empty() const {
        std::lock_guard<std::mutex> lock(mutex_);
        return queue_.empty();
    }
};

// CPU使用率监控
class CPUMonitor {
private:
    uint64_t last_total_ = 0;
    uint64_t last_idle_ = 0;
    
public:
    float get_usage() {
        std::ifstream file("/proc/stat");
        std::string line;
        std::getline(file, line);
        
        std::istringstream iss(line);
        std::string cpu;
        uint64_t user, nice, system, idle, iowait, irq, softirq, steal;
        iss >> cpu >> user >> nice >> system >> idle >> iowait >> irq >> softirq >> steal;
        
        uint64_t total = user + nice + system + idle + iowait + irq + softirq + steal;
        uint64_t total_diff = total - last_total_;
        uint64_t idle_diff = idle - last_idle_;
        
        float usage = 0.0f;
        if (total_diff > 0) {
            usage = 100.0f * (1.0f - (float)idle_diff / total_diff);
        }
        
        last_total_ = total;
        last_idle_ = idle;
        
        return usage;
    }
};

// 日志工具
enum LogLevel {
    LOG_DEBUG = 0,
    LOG_INFO = 1,
    LOG_WARN = 2,
    LOG_ERROR = 3
};

class Logger {
private:
    static LogLevel level_;
    static std::mutex mutex_;
    
public:
    static void set_level(LogLevel level) { level_ = level; }
    
    template<typename... Args>
    static void log(LogLevel level, const char* format, Args... args) {
        if (level < level_) return;
        
        std::lock_guard<std::mutex> lock(mutex_);
        
        auto now = std::chrono::system_clock::now();
        auto time_t = std::chrono::system_clock::to_time_t(now);
        auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(
            now.time_since_epoch()) % 1000;
        
        char time_str[100];
        strftime(time_str, sizeof(time_str), "%Y-%m-%d %H:%M:%S", localtime(&time_t));
        
        const char* level_str[] = {"DEBUG", "INFO", "WARN", "ERROR"};
        printf("[%s.%03d] [%s] ", time_str, (int)ms.count(), level_str[level]);
        printf(format, args...);
        printf("\n");
        fflush(stdout);
    }
};

#define LOG_D(...) Logger::log(LOG_DEBUG, __VA_ARGS__)
#define LOG_I(...) Logger::log(LOG_INFO, __VA_ARGS__)
#define LOG_W(...) Logger::log(LOG_WARN, __VA_ARGS__)
#define LOG_E(...) Logger::log(LOG_ERROR, __VA_ARGS__)

// 静态成员定义
LogLevel Logger::level_ = LOG_INFO;
std::mutex Logger::mutex_;

#endif // COMMON_H
