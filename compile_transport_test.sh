#!/bin/bash

# 编译RTSP传输协议测试程序

echo "Compiling RTSP Transport Test..."

# 检查必要的头文件
if [ ! -f "include/video_capture.h" ]; then
    echo "Error: include/video_capture.h not found"
    exit 1
fi

if [ ! -f "include/capture_config.h" ]; then
    echo "Error: include/capture_config.h not found"
    exit 1
fi

# 设置编译参数
CXX="g++"
CXXFLAGS="-std=c++17 -Wall -Wextra -O2"
INCLUDES="-I. -I./include"

# 获取库依赖
FFMPEG_LIBS=$(pkg-config --libs libavformat libavcodec libavutil 2>/dev/null)
if [ $? -ne 0 ]; then
    echo "Warning: pkg-config for FFmpeg failed, using manual flags"
    FFMPEG_LIBS="-lavformat -lavcodec -lavutil"
fi

FFMPEG_CFLAGS=$(pkg-config --cflags libavformat libavcodec libavutil 2>/dev/null)

# 其他必要的库
OTHER_LIBS="-lpthread -lm"

# 编译命令
COMPILE_CMD="$CXX $CXXFLAGS $INCLUDES $FFMPEG_CFLAGS test_rtsp_transport.cpp -o test_rtsp_transport $FFMPEG_LIBS $OTHER_LIBS"

echo "Compile command:"
echo "$COMPILE_CMD"
echo ""

# 执行编译
if $COMPILE_CMD; then
    echo "✓ Compilation successful!"
    echo ""
    echo "Usage examples:"
    echo "  ./test_rtsp_transport rtsp://192.168.1.100:554/stream"
    echo "  ./test_rtsp_transport rtsp://admin:<EMAIL>/stream test_both"
    echo ""
    echo "The program will test:"
    echo "  - UDP transport (default, low latency)"
    echo "  - TCP transport (optional, high reliability)"
    echo "  - Connection time and frame acquisition performance"
else
    echo "✗ Compilation failed!"
    echo ""
    echo "Common issues and solutions:"
    echo "1. Missing FFmpeg development packages:"
    echo "   sudo apt-get install libavformat-dev libavcodec-dev libavutil-dev"
    echo ""
    echo "2. Missing pkg-config:"
    echo "   sudo apt-get install pkg-config"
    echo ""
    echo "3. Check if all header files exist:"
    echo "   ls -la include/"
    exit 1
fi
