
class VideoCaptureService {
public:
    bool init(const CaptureConfig& config) {
        if (config.source_type == V4L2_SOURCE) {
            // 使用DMA缓冲区优化
            if (!v4l2_init(config.device, config.width, config.height, V4L2_MEMORY_DMABUF)) {
                // 回退到MMAP
                v4l2_init(config.device, config.width, config.height, V4L2_MEMORY_MMAP);
            }
        } else if (config.source_type == RTSP_SOURCE) {
            rtsp_client.init(config.url, config.use_tcp);
        }
        
        // 创建Fast DDS Writer
        dds_writer = create_dds_writer("VideoFrames");
        return true;
    }

    void run() {
        while (!stop_requested) {
            auto frame = capture_frame();
            if (frame.valid) {
                publish_frame(frame);
            }
            // 动态帧率控制
            adjust_capture_rate();
        }
    }

private:
    Frame capture_frame() {
        if (source_type == V4L2_SOURCE) {
            v4l2_buffer buf = {};
            ioctl(fd, VIDIOC_DQBUF, &buf);
            return {
                .timestamp = get_current_us(),
                .dma_fd = buf.m.fd,
                .size = buf.bytesused,
                .format = current_format,
                .width = current_width,
                .height = current_height,
                .is_keyframe = true  // V4L2帧都是关键帧
            };
        } else {
            // RTSP使用硬件解码
            AVFrame* av_frame = rtsp_client.get_frame();
            return {
                .timestamp = av_frame->pts,
                .data = av_frame->data[0],
                .size = av_frame->pkt_size,
                .format = (av_frame->format == AV_CODEC_ID_H264) ? H264 : H265,
                .width = av_frame->width,
                .height = av_frame->height,
                .is_keyframe = !!(av_frame->flags & AV_FRAME_FLAG_KEY)
            };
        }
    }

    void publish_frame(const Frame& frame) {
        VideoFrame dds_frame;
        dds_frame.frame_id = frame_id_counter++;
        dds_frame.timestamp = frame.timestamp;
        dds_frame.width = frame.width;
        dds_frame.height = frame.height;
        dds_frame.format = frame.format;
        dds_frame.is_keyframe = frame.is_keyframe;
        
        // 零拷贝传递
        if (frame.dma_fd > 0) {
            dds_frame.dma_fd = frame.dma_fd;
            dds_frame.data_length = frame.size;
        } else {
            dds_frame.data.resize(frame.size);
            memcpy(dds_frame.data.data(), frame.data, frame.size);
        }
        
        dds_writer->write(dds_frame);
        
        // 释放资源 (RTSP软缓冲)
        if (frame.data != nullptr) {
            rtsp_client.release_frame(frame.data);
        }
    }
    
    void adjust_capture_rate() {
        static int frame_interval = 33; // 30fps
        static auto last_adjust = std::chrono::steady_clock::now();
        
        auto now = std::chrono::steady_clock::now();
        if (now - last_adjust > std::chrono::seconds(5)) {
            float cpu_load = get_cpu_usage();
            if (cpu_load > 80.0f) {
                frame_interval = std::min(frame_interval + 5, 100); // 最低10fps
            } else if (cpu_load < 50.0f && frame_interval > 33) {
                frame_interval = std::max(frame_interval - 5, 33);
            }
            set_frame_interval(frame_interval);
            last_adjust = now;
        }
        
        std::this_thread::sleep_for(std::chrono::milliseconds(frame_interval));
    }
};

