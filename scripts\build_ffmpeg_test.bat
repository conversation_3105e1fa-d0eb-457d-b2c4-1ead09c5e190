@echo off
REM FFmpeg RTSP测试构建脚本 (Windows)

echo === FFmpeg RTSP Client Build Test ===

REM 检查是否安装了FFmpeg
where ffmpeg >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: FFmpeg not found in PATH
    echo Please install FFmpeg and add it to PATH
    echo Download from: https://ffmpeg.org/download.html
    exit /b 1
)

echo FFmpeg found in PATH

REM 检查pkg-config
where pkg-config >nul 2>&1
if %errorlevel% neq 0 (
    echo Warning: pkg-config not found
    echo Using default FFmpeg paths
    set FFMPEG_INCLUDE=-IC:\ffmpeg\include
    set FFMPEG_LIBS=-LC:\ffmpeg\lib -lavformat -lavcodec -lavutil
) else (
    echo Using pkg-config for FFmpeg flags
    for /f "delims=" %%i in ('pkg-config --cflags libavformat libavcodec libavutil') do set FFMPEG_INCLUDE=%%i
    for /f "delims=" %%i in ('pkg-config --libs libavformat libavcodec libavutil') do set FFMPEG_LIBS=%%i
)

echo FFmpeg include flags: %FFMPEG_INCLUDE%
echo FFmpeg library flags: %FFMPEG_LIBS%

REM 创建构建目录
if not exist build mkdir build
cd build

REM 设置编译器
set CXX=g++
set CXXFLAGS=-std=c++17 -Wall -Wextra -I../include

REM 检查编译器类型
where cl >nul 2>&1
if %errorlevel% equ 0 (
    echo Using MSVC compiler
    set CXX=cl
    set CXXFLAGS=/std:c++17 /W3 /I..\include
    set FFMPEG_LIBS=avformat.lib avcodec.lib avutil.lib
)

echo Compiler: %CXX%
echo Flags: %CXXFLAGS%

REM 编译测试程序
echo.
echo Compiling FFmpeg RTSP test...
%CXX% %CXXFLAGS% %FFMPEG_INCLUDE% -o test_rtsp_ffmpeg.exe ../test/test_rtsp_ffmpeg.cpp %FFMPEG_LIBS%

if %errorlevel% neq 0 (
    echo.
    echo Build failed!
    cd ..
    exit /b 1
)

echo.
echo Build successful!
echo Executable: build/test_rtsp_ffmpeg.exe
echo.
echo Usage: test_rtsp_ffmpeg.exe <rtsp_url> [tcp]
echo Example: test_rtsp_ffmpeg.exe rtsp://192.168.1.100:554/stream tcp
echo.

cd ..
