
class AIProcessor {
public:
    void init(const std::string& model_path) {
        // 创建DDS Reader
        input_reader = create_reader("AI_Frames");
        
        // 初始化AI模型
        ai_engine.load(model_path);
    }

    void run() {
        while (!stop_requested) {
            VideoFrame frame;
            if (input_reader->take_next_sample(&frame)) {
                process_frame(frame);
            }
        }
    }

private:
    void process_frame(const VideoFrame& frame) {
        if (frame.dma_fd > 0) {
            // 零拷贝处理
            ai_engine.process_dma(frame.dma_fd, frame.width, frame.height);
        } else {
            // 软件路径
            ai_engine.process(frame.data.data(), frame.width, frame.height);
        }
        
        // 释放DMA资源
        if (frame.dma_fd > 0) {
            close(frame.dma_fd);
        }
    }
};

