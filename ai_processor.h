
#ifndef AI_PROCESSOR_H
#define AI_PROCESSOR_H

#include "common.h"
#include "dds_interface.h"
#include <atomic>
#include <thread>
#include <vector>

// AI检测结果
struct Detection {
    int class_id;
    float confidence;
    float x, y, width, height;  // 归一化坐标
    std::string class_name;
};

struct AIResult {
    uint64_t frame_id;
    uint64_t timestamp;
    std::vector<Detection> detections;
    float inference_time_ms;
    bool valid;
};

// AI引擎接口
class AIEngine {
public:
    virtual ~AIEngine() = default;
    virtual bool init(const AIConfig& config) = 0;
    virtual AIResult process(const Frame& frame) = 0;
    virtual void cleanup() = 0;
    virtual std::string get_engine_name() const = 0;
};

// TensorRT引擎实现
class TensorRTEngine : public AIEngine {
private:
    void* engine_ = nullptr;
    void* context_ = nullptr;
    void* cuda_stream_ = nullptr;
    void* input_buffer_ = nullptr;
    void* output_buffer_ = nullptr;

    int input_width_ = 640;
    int input_height_ = 640;
    int num_classes_ = 80;
    float confidence_threshold_ = 0.5f;

public:
    bool init(const AIConfig& config) override;
    AIResult process(const Frame& frame) override;
    void cleanup() override;
    std::string get_engine_name() const override { return "TensorRT"; }

private:
    bool load_engine(const std::string& engine_path);
    bool preprocess(const Frame& frame, float* input_data);
    std::vector<Detection> postprocess(float* output_data, int output_size);
};

// ONNX引擎实现
class ONNXEngine : public AIEngine {
private:
    void* session_ = nullptr;
    void* memory_info_ = nullptr;

    int input_width_ = 640;
    int input_height_ = 640;
    int num_classes_ = 80;
    float confidence_threshold_ = 0.5f;

public:
    bool init(const AIConfig& config) override;
    AIResult process(const Frame& frame) override;
    void cleanup() override;
    std::string get_engine_name() const override { return "ONNX"; }
};

class AIProcessor {
private:
    AIConfig config_;
    std::atomic<bool> stop_requested_{false};
    std::atomic<bool> running_{false};
    std::thread processor_thread_;

    // DDS接口
    std::unique_ptr<DDSReader> input_reader_;
    std::unique_ptr<DDSWriter> result_writer_;  // 可选：发布AI结果

    // AI引擎
    std::unique_ptr<AIEngine> ai_engine_;

    // 统计信息
    std::atomic<uint64_t> frames_processed_{0};
    std::atomic<uint64_t> frames_dropped_{0};
    std::atomic<uint64_t> total_inference_time_ms_{0};

    // 性能监控
    CPUMonitor cpu_monitor_;
    std::chrono::steady_clock::time_point last_stats_time_;

    // 动态处理控制
    std::atomic<int> process_interval_ms_{0};  // 0表示处理每一帧

public:
    AIProcessor() = default;
    ~AIProcessor() { stop(); }

    bool init(const AIConfig& config) {
        config_ = config;

        // 创建DDS Reader
        input_reader_ = std::make_unique<DDSReader>();
        if (!input_reader_->init("AI_Frames")) {
            LOG_E("Failed to initialize AI input reader");
            return false;
        }

        // 可选：创建结果发布器
        result_writer_ = std::make_unique<DDSWriter>();
        if (!result_writer_->init("AI_Results")) {
            LOG_W("Failed to initialize AI result writer, results won't be published");
            result_writer_.reset();
        }

        // 创建AI引擎
        if (config.engine_type == "tensorrt") {
            ai_engine_ = std::make_unique<TensorRTEngine>();
        } else if (config.engine_type == "onnx") {
            ai_engine_ = std::make_unique<ONNXEngine>();
        } else {
            LOG_E("Unsupported AI engine type: %s", config.engine_type.c_str());
            return false;
        }

        if (!ai_engine_->init(config)) {
            LOG_E("Failed to initialize AI engine");
            return false;
        }

        last_stats_time_ = std::chrono::steady_clock::now();

        LOG_I("AI processor initialized with %s engine", ai_engine_->get_engine_name().c_str());
        return true;
    }

    void start() {
        if (running_.load()) {
            LOG_W("AI processor already running");
            return;
        }

        stop_requested_.store(false);
        processor_thread_ = std::thread(&AIProcessor::run, this);
        running_.store(true);
        LOG_I("AI processor started");
    }

    void stop() {
        if (!running_.load()) {
            return;
        }

        stop_requested_.store(true);
        if (processor_thread_.joinable()) {
            processor_thread_.join();
        }
        running_.store(false);

        if (ai_engine_) {
            ai_engine_->cleanup();
        }

        LOG_I("AI processor stopped");
    }

    void run() {
        LOG_I("AI processor thread started");

        while (!stop_requested_.load()) {
            try {
                Frame input_frame;
                if (input_reader_->read(input_frame, 100)) {  // 100ms超时

                    // 动态处理控制
                    if (should_process_frame()) {
                        process_frame(input_frame);
                        frames_processed_.fetch_add(1);
                    } else {
                        frames_dropped_.fetch_add(1);
                    }

                    // 定期输出统计信息
                    print_stats();

                } else {
                    // 没有数据时短暂休眠
                    std::this_thread::sleep_for(std::chrono::milliseconds(50));
                }
            } catch (const std::exception& e) {
                LOG_E("AI processor loop exception: %s", e.what());
                frames_dropped_.fetch_add(1);
                std::this_thread::sleep_for(std::chrono::milliseconds(100));
            }
        }

        LOG_I("AI processor thread stopped");
    }

    // 获取统计信息
    struct Stats {
        uint64_t frames_processed;
        uint64_t frames_dropped;
        float avg_inference_time_ms;
        float fps;
        float cpu_usage;
    };

    Stats get_stats() {
        uint64_t processed = frames_processed_.load();
        uint64_t total_time = total_inference_time_ms_.load();

        float avg_inference_time = 0.0f;
        if (processed > 0) {
            avg_inference_time = (float)total_time / processed;
        }

        // 计算FPS
        static auto last_time = std::chrono::steady_clock::now();
        static uint64_t last_processed = 0;

        auto now = std::chrono::steady_clock::now();
        auto elapsed = std::chrono::duration_cast<std::chrono::seconds>(now - last_time).count();

        float fps = 0.0f;
        if (elapsed > 0) {
            fps = (float)(processed - last_processed) / elapsed;
        }

        last_time = now;
        last_processed = processed;

        return {
            .frames_processed = processed,
            .frames_dropped = frames_dropped_.load(),
            .avg_inference_time_ms = avg_inference_time,
            .fps = fps,
            .cpu_usage = cpu_monitor_.get_usage()
        };
    }

    // 信号处理
    void handle_signal(int signal) {
        switch (signal) {
            case SIGUSR1:
                // 降低AI处理频率
                process_interval_ms_.store(std::min(process_interval_ms_.load() + 100, 1000));
                LOG_I("Signal SIGUSR1: reduced AI processing frequency to %d ms interval",
                      process_interval_ms_.load());
                break;
            case SIGUSR2:
                // 恢复AI处理频率
                process_interval_ms_.store(std::max(process_interval_ms_.load() - 100, 0));
                LOG_I("Signal SIGUSR2: increased AI processing frequency to %d ms interval",
                      process_interval_ms_.load());
                break;
            default:
                break;
        }
    }

private:
    bool should_process_frame() {
        int interval = process_interval_ms_.load();
        if (interval == 0) {
            return true;  // 处理每一帧
        }

        static auto last_process_time = std::chrono::steady_clock::now();
        auto now = std::chrono::steady_clock::now();
        auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(
            now - last_process_time).count();

        if (elapsed >= interval) {
            last_process_time = now;
            return true;
        }

        return false;
    }

    void process_frame(const Frame& frame) {
        if (!ai_engine_) {
            return;
        }

        auto start_time = std::chrono::high_resolution_clock::now();

        // 执行AI推理
        AIResult result = ai_engine_->process(frame);

        auto end_time = std::chrono::high_resolution_clock::now();
        auto inference_time = std::chrono::duration_cast<std::chrono::milliseconds>(
            end_time - start_time).count();

        total_inference_time_ms_.fetch_add(inference_time);

        if (result.valid) {
            LOG_D("Frame %lu: %zu detections, inference time: %.2f ms",
                  frame.frame_id, result.detections.size(), result.inference_time_ms);

            // 处理检测结果
            handle_detections(result);

            // 可选：发布结果
            if (result_writer_) {
                // 这里需要定义AI结果的DDS类型
                // result_writer_->write(result);
            }
        } else {
            LOG_W("AI inference failed for frame %lu", frame.frame_id);
        }
    }

    void handle_detections(const AIResult& result) {
        // 处理检测结果，例如：
        // 1. 记录到日志
        // 2. 触发报警
        // 3. 保存关键帧
        // 4. 发送通知等

        for (const auto& detection : result.detections) {
            if (detection.confidence > config_.confidence_threshold) {
                LOG_I("Detected %s (%.2f%%) at [%.2f, %.2f, %.2f, %.2f]",
                      detection.class_name.c_str(),
                      detection.confidence * 100,
                      detection.x, detection.y,
                      detection.width, detection.height);
            }
        }
    }

    void print_stats() {
        auto now = std::chrono::steady_clock::now();
        auto elapsed = std::chrono::duration_cast<std::chrono::seconds>(
            now - last_stats_time_).count();

        // 每30秒输出一次统计信息
        if (elapsed >= 30) {
            auto stats = get_stats();
            LOG_I("AI Stats - Processed: %lu, Dropped: %lu, FPS: %.1f, "
                  "Avg inference: %.1f ms, CPU: %.1f%%",
                  stats.frames_processed, stats.frames_dropped, stats.fps,
                  stats.avg_inference_time_ms, stats.cpu_usage);

            last_stats_time_ = now;
        }
    }
};

// TensorRT引擎实现（简化版）
bool TensorRTEngine::init(const AIConfig& config) {
    confidence_threshold_ = config.confidence_threshold;

    // 这里应该加载TensorRT引擎
    // 为了简化，我们只是记录日志
    LOG_I("TensorRT engine initialized (simplified implementation)");
    LOG_I("Model: %s, Confidence threshold: %.2f",
          config.model_path.c_str(), confidence_threshold_);

    return true;
}

AIResult TensorRTEngine::process(const Frame& frame) {
    AIResult result;
    result.frame_id = frame.frame_id;
    result.timestamp = frame.timestamp;
    result.valid = false;

    if (!frame.valid || frame.data.empty()) {
        return result;
    }

    auto start_time = std::chrono::high_resolution_clock::now();

    // 简化的推理实现
    // 实际应该包括：
    // 1. 预处理（resize, normalize等）
    // 2. 推理执行
    // 3. 后处理（NMS, 坐标转换等）

    // 模拟检测结果
    if (frame.frame_id % 10 == 0) {  // 每10帧模拟一个检测
        Detection det;
        det.class_id = 0;
        det.class_name = "person";
        det.confidence = 0.85f;
        det.x = 0.3f;
        det.y = 0.2f;
        det.width = 0.2f;
        det.height = 0.4f;

        result.detections.push_back(det);
    }

    auto end_time = std::chrono::high_resolution_clock::now();
    result.inference_time_ms = std::chrono::duration_cast<std::chrono::microseconds>(
        end_time - start_time).count() / 1000.0f;

    result.valid = true;
    return result;
}

void TensorRTEngine::cleanup() {
    // 清理TensorRT资源
    LOG_I("TensorRT engine cleanup");
}

// ONNX引擎实现（简化版）
bool ONNXEngine::init(const AIConfig& config) {
    confidence_threshold_ = config.confidence_threshold;

    LOG_I("ONNX engine initialized (simplified implementation)");
    LOG_I("Model: %s, Confidence threshold: %.2f",
          config.model_path.c_str(), confidence_threshold_);

    return true;
}

AIResult ONNXEngine::process(const Frame& frame) {
    AIResult result;
    result.frame_id = frame.frame_id;
    result.timestamp = frame.timestamp;
    result.valid = false;

    if (!frame.valid || frame.data.empty()) {
        return result;
    }

    auto start_time = std::chrono::high_resolution_clock::now();

    // 简化的ONNX推理实现
    // 模拟检测结果
    if (frame.frame_id % 15 == 0) {  // 每15帧模拟一个检测
        Detection det;
        det.class_id = 1;
        det.class_name = "car";
        det.confidence = 0.75f;
        det.x = 0.5f;
        det.y = 0.3f;
        det.width = 0.3f;
        det.height = 0.2f;

        result.detections.push_back(det);
    }

    auto end_time = std::chrono::high_resolution_clock::now();
    result.inference_time_ms = std::chrono::duration_cast<std::chrono::microseconds>(
        end_time - start_time).count() / 1000.0f;

    result.valid = true;
    return result;
}

void ONNXEngine::cleanup() {
    // 清理ONNX资源
    LOG_I("ONNX engine cleanup");
}

#endif // AI_PROCESSOR_H

