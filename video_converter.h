#ifndef VIDEO_CONVERTER_H
#define VIDEO_CONVERTER_H

#include "common.h"
#include "dds_interface.h"
#include <atomic>
#include <thread>

// 硬件加速接口
class HardwareAccelerator {
private:
    int v4l2_conv_fd_ = -1;
    AVBufferRef* hw_device_ctx_ = nullptr;
    AVBufferRef* hw_frames_ctx_ = nullptr;
    bool vaapi_available_ = false;

public:
    HardwareAccelerator() = default;
    ~HardwareAccelerator() { cleanup(); }

    bool init();
    bool convert_format(const Frame& src, Frame& dst, FrameFormat target_format);
    bool encode_h264(const Frame& src, Frame& dst);
    void cleanup();

private:
    bool init_v4l2_converter();
    bool init_vaapi();
    bool v4l2_convert(const Frame& src, Frame& dst, FrameFormat target_format);
    bool vaapi_encode(const Frame& src, Frame& dst);
};

class VideoConverter {
private:
    std::atomic<bool> stop_requested_{false};
    std::atomic<bool> running_{false};
    std::thread converter_thread_;

    // DDS接口
    std::unique_ptr<DDSReader> input_reader_;
    std::unique_ptr<DDSWriter> ai_writer_;
    std::unique_ptr<DDSWriter> cloud_writer_;

    // 硬件加速
    std::unique_ptr<HardwareAccelerator> hw_accel_;

    // 统计信息
    std::atomic<uint64_t> frames_processed_{0};
    std::atomic<uint64_t> frames_dropped_{0};

    // 性能监控
    CPUMonitor cpu_monitor_;

public:
    VideoConverter() = default;
    ~VideoConverter() { stop(); }

    bool init() {
        // 创建DDS读写器
        input_reader_ = std::make_unique<DDSReader>();
        if (!input_reader_->init("VideoFrames")) {
            LOG_E("Failed to initialize input reader");
            return false;
        }

        ai_writer_ = std::make_unique<DDSWriter>();
        if (!ai_writer_->init("AI_Frames")) {
            LOG_E("Failed to initialize AI writer");
            return false;
        }

        cloud_writer_ = std::make_unique<DDSWriter>();
        if (!cloud_writer_->init("Cloud_Frames")) {
            LOG_E("Failed to initialize cloud writer");
            return false;
        }

        // 初始化硬件加速
        hw_accel_ = std::make_unique<HardwareAccelerator>();
        if (!hw_accel_->init()) {
            LOG_W("Hardware acceleration not available, using software fallback");
        }

        LOG_I("Video converter initialized");
        return true;
    }

    void start() {
        if (running_.load()) {
            LOG_W("Video converter already running");
            return;
        }

        stop_requested_.store(false);
        converter_thread_ = std::thread(&VideoConverter::run, this);
        running_.store(true);
        LOG_I("Video converter started");
    }

    void stop() {
        if (!running_.load()) {
            return;
        }

        stop_requested_.store(true);
        if (converter_thread_.joinable()) {
            converter_thread_.join();
        }
        running_.store(false);

        LOG_I("Video converter stopped");
    }

    void run() {
        LOG_I("Video converter thread started");

        while (!stop_requested_.load()) {
            try {
                Frame input_frame;
                if (input_reader_->read(input_frame, 100)) {  // 100ms超时
                    process_frame(input_frame);
                    frames_processed_.fetch_add(1);
                } else {
                    // 没有数据时短暂休眠
                    std::this_thread::sleep_for(std::chrono::milliseconds(10));
                }
            } catch (const std::exception& e) {
                LOG_E("Converter loop exception: %s", e.what());
                frames_dropped_.fetch_add(1);
                std::this_thread::sleep_for(std::chrono::milliseconds(100));
            }
        }

        LOG_I("Video converter thread stopped");
    }

    // 获取统计信息
    struct Stats {
        uint64_t frames_processed;
        uint64_t frames_dropped;
        float cpu_usage;
    };

    Stats get_stats() {
        return {
            .frames_processed = frames_processed_.load(),
            .frames_dropped = frames_dropped_.load(),
            .cpu_usage = cpu_monitor_.get_usage()
        };
    }


    // 信号处理
    void handle_signal(int signal) {
        if (signal == SIGUSR1) {
            // 清理缓存，释放内存
            LOG_I("Signal SIGUSR1: cleaning up memory");
            // 这里可以添加内存清理逻辑
        }
    }

private:
    void process_frame(const Frame& input_frame) {
        // 并行处理两路输出
        std::thread ai_thread([this, input_frame] {
            try {
                convert_for_ai(input_frame);
            } catch (const std::exception& e) {
                LOG_E("AI conversion exception: %s", e.what());
                frames_dropped_.fetch_add(1);
            }
        });

        std::thread cloud_thread([this, input_frame] {
            try {
                convert_for_cloud(input_frame);
            } catch (const std::exception& e) {
                LOG_E("Cloud conversion exception: %s", e.what());
                frames_dropped_.fetch_add(1);
            }
        });

        ai_thread.detach();
        cloud_thread.detach();
    }

    void convert_for_ai(const Frame& src) {
        Frame dst;
        dst.frame_id = src.frame_id;
        dst.timestamp = src.timestamp;
        dst.width = src.width;
        dst.height = src.height;
        dst.format = BGR24;  // AI需要BGR格式
        dst.source_type = src.source_type;
        dst.is_keyframe = src.is_keyframe;

        // 尝试硬件加速转换
        bool hw_success = false;
        if (hw_accel_) {
            hw_success = hw_accel_->convert_format(src, dst, BGR24);
        }

        // 硬件转换失败，使用软件转换
        if (!hw_success) {
            if (!software_convert(src, dst, BGR24)) {
                LOG_E("Failed to convert frame %lu for AI", src.frame_id);
                return;
            }
        }

        dst.valid = true;

        // 发送给AI处理模块
        if (!ai_writer_->write(dst)) {
            LOG_W("Failed to send frame %lu to AI processor", dst.frame_id);
        }
    }

    void convert_for_cloud(const Frame& src) {
        Frame dst;
        dst.frame_id = src.frame_id;
        dst.timestamp = src.timestamp;
        dst.source_type = src.source_type;
        dst.is_keyframe = src.is_keyframe;

        // 如果已经是H264/H265，直接转发
        if (src.format == H264 || src.format == H265) {
            dst = std::move(const_cast<Frame&>(src));  // 移动语义
        }
        // 需要编码
        else {
            dst.format = H264;
            dst.width = src.width;
            dst.height = src.height;

            // 尝试硬件编码
            bool hw_success = false;
            if (hw_accel_) {
                hw_success = hw_accel_->encode_h264(src, dst);
            }

            // 硬件编码失败，使用软件编码
            if (!hw_success) {
                if (!software_encode_h264(src, dst)) {
                    LOG_E("Failed to encode frame %lu for cloud", src.frame_id);
                    return;
                }
            }
        }

        dst.valid = true;

        // 发送给云端推流模块
        if (!cloud_writer_->write(dst)) {
            LOG_W("Failed to send frame %lu to cloud streamer", dst.frame_id);
        }
    }

    // 软件格式转换
    bool software_convert(const Frame& src, Frame& dst, FrameFormat target_format) {
        if (src.data.empty()) {
            LOG_E("Source frame data is empty");
            return false;
        }

        // 简化实现：只支持YUV420到BGR24的转换
        if (src.format == YUV420 && target_format == BGR24) {
            size_t bgr_size = src.width * src.height * 3;
            dst.data.resize(bgr_size);

            // 使用简单的YUV到BGR转换
            const uint8_t* y_plane = src.data.data();
            const uint8_t* u_plane = y_plane + src.width * src.height;
            const uint8_t* v_plane = u_plane + (src.width * src.height) / 4;

            for (int row = 0; row < src.height; ++row) {
                for (int col = 0; col < src.width; ++col) {
                    int y_idx = row * src.width + col;
                    int uv_idx = (row / 2) * (src.width / 2) + (col / 2);

                    int y = y_plane[y_idx];
                    int u = u_plane[uv_idx] - 128;
                    int v = v_plane[uv_idx] - 128;

                    // YUV to RGB conversion
                    int r = y + (1.402 * v);
                    int g = y - (0.344 * u) - (0.714 * v);
                    int b = y + (1.772 * u);

                    // Clamp values
                    r = std::max(0, std::min(255, r));
                    g = std::max(0, std::min(255, g));
                    b = std::max(0, std::min(255, b));

                    // Store as BGR
                    int bgr_idx = (row * src.width + col) * 3;
                    dst.data[bgr_idx] = b;
                    dst.data[bgr_idx + 1] = g;
                    dst.data[bgr_idx + 2] = r;
                }
            }

            return true;
        }

        LOG_E("Unsupported software conversion: %d -> %d", src.format, target_format);
        return false;
    }

    // 软件H264编码
    bool software_encode_h264(const Frame& src, Frame& dst) {
        // 这里应该使用x264库进行编码
        // 为了简化，我们只是复制数据并标记为H264
        LOG_W("Software H264 encoding not fully implemented, copying raw data");

        dst.data = src.data;
        dst.format = H264;
        dst.width = src.width;
        dst.height = src.height;

        return true;
    }
};

// HardwareAccelerator实现
bool HardwareAccelerator::init() {
    bool success = false;

    // 初始化V4L2转换器
    if (init_v4l2_converter()) {
        LOG_I("V4L2 converter initialized");
        success = true;
    }

    // 初始化VAAPI
    if (init_vaapi()) {
        LOG_I("VAAPI encoder initialized");
        success = true;
    }

    return success;
}

bool HardwareAccelerator::init_v4l2_converter() {
    // 尝试打开V4L2转换设备
    const char* conv_devices[] = {
        "/dev/video10",  // 常见的转换设备
        "/dev/video11",
        "/dev/video12"
    };

    for (const char* device : conv_devices) {
        v4l2_conv_fd_ = open(device, O_RDWR | O_NONBLOCK);
        if (v4l2_conv_fd_ >= 0) {
            // 检查设备能力
            v4l2_capability cap;
            if (ioctl(v4l2_conv_fd_, VIDIOC_QUERYCAP, &cap) == 0) {
                if (cap.capabilities & V4L2_CAP_VIDEO_M2M) {
                    LOG_I("Found V4L2 converter device: %s", device);
                    return true;
                }
            }
            close(v4l2_conv_fd_);
            v4l2_conv_fd_ = -1;
        }
    }

    LOG_W("No V4L2 converter device found");
    return false;
}

bool HardwareAccelerator::init_vaapi() {
    // 初始化VAAPI设备上下文
    int ret = av_hwdevice_ctx_create(&hw_device_ctx_, AV_HWDEVICE_TYPE_VAAPI,
                                   nullptr, nullptr, 0);
    if (ret < 0) {
        LOG_W("Failed to create VAAPI device context: %s", av_err2str(ret));
        return false;
    }

    vaapi_available_ = true;
    return true;
}

bool HardwareAccelerator::convert_format(const Frame& src, Frame& dst, FrameFormat target_format) {
    if (v4l2_conv_fd_ >= 0) {
        return v4l2_convert(src, dst, target_format);
    }
    return false;
}

bool HardwareAccelerator::encode_h264(const Frame& src, Frame& dst) {
    if (vaapi_available_) {
        return vaapi_encode(src, dst);
    }
    return false;
}

bool HardwareAccelerator::v4l2_convert(const Frame& src, Frame& dst, FrameFormat target_format) {
    // V4L2转换实现（简化版）
    LOG_D("V4L2 hardware conversion not fully implemented");
    return false;
}

bool HardwareAccelerator::vaapi_encode(const Frame& src, Frame& dst) {
    // VAAPI编码实现（简化版）
    LOG_D("VAAPI hardware encoding not fully implemented");
    return false;
}

void HardwareAccelerator::cleanup() {
    if (hw_frames_ctx_) {
        av_buffer_unref(&hw_frames_ctx_);
    }

    if (hw_device_ctx_) {
        av_buffer_unref(&hw_device_ctx_);
    }

    if (v4l2_conv_fd_ >= 0) {
        close(v4l2_conv_fd_);
        v4l2_conv_fd_ = -1;
    }

    vaapi_available_ = false;
}

#endif // VIDEO_CONVERTER_H

