class VideoConverter {
public:
    void init() {
        // 创建DDS读写器
        input_reader = create_reader("VideoFrames");
        ai_writer = create_writer("AI_Frames");
        cloud_writer = create_writer("Cloud_Frames");
        
        // 初始化硬件加速
        v4l2_conv_init(V4L2_PIX_FMT_BGR24); // AI需要BGR
        vaapi_encoder_init(AV_CODEC_ID_H264); // 云端推送需要H264
    }

    void run() {
        while (!stop_requested) {
            if (input_reader->wait_for_samples(5)) { // 5ms超时
                process_available_frames();
            }
        }
    }

private:
    void process_available_frames() {
        VideoFrame frame;
        while (input_reader->take_next_sample(&frame)) {
            // 并行处理两路输出
            std::thread ai_thread([this, frame] {
                convert_for_ai(frame);
            });
            
            std::thread cloud_thread([this, frame] {
                convert_for_cloud(frame);
            });
            
            ai_thread.detach();
            cloud_thread.detach();
        }
    }

    void convert_for_ai(const VideoFrame& src) {
        VideoFrame dst;
        dst.frame_id = src.frame_id;
        dst.timestamp = src.timestamp;
        dst.width = src.width;
        dst.height = src.height;
        dst.format = BGR24; // AI需要BGR格式
        
        // 硬件加速转换
        if (src.dma_fd > 0) {
            dst.dma_fd = v4l2_convert(src.dma_fd, src.format, BGR24);
            dst.data_length = src.width * src.height * 3;
        } else {
            // 软件转换
            dst.data.resize(src.width * src.height * 3);
            libyuv_convert(src, dst.data.data(), BGR24);
        }
        
        ai_writer->write(dst);
    }

    void convert_for_cloud(const VideoFrame& src) {
        VideoFrame dst;
        dst.frame_id = src.frame_id;
        dst.timestamp = src.timestamp;
        
        // 如果已经是H264/H265，直接转发
        if (src.format == H264 || src.format == H265) {
            dst = src; // 直接复用
        } 
        // 需要编码
        else {
            dst.format = H264;
            dst.width = src.width;
            dst.height = src.height;
            
            // 硬件编码
            if (src.dma_fd > 0) {
                dst.dma_fd = vaapi_encode(src.dma_fd, src.format, H264);
            } else {
                // 软件编码
                dst.data = x264_encode(src.data.data(), src.width, src.height);
            }
        }
        
        cloud_writer->write(dst);
    }
};

