#!/bin/sh

# 配置共享内存
mount -t tmpfs -o size=32m tmpfs /tmp/dds_shm
mkdir -p /tmp/dds_shm/video_service

# 配置DMA缓冲区池
echo "4x1920x1080" > /sys/module/videobuf2_core/parameters/capture_pool
echo "4x1920x1080" > /sys/module/videobuf2_core/parameters/convert_pool
echo "2x1920x1080" > /sys/module/videobuf2_core/parameters/cloud_pool

# 设置CPU亲和性
taskset 0x1 ./video_capture --source v4l2 --device /dev/video0 &
taskset 0x2 ./video_converter &
taskset 0x4 ./ai_processor --model obj_detect.trt &
taskset 0x8 ./cloud_streamer --type webrtc --url "stun:cloud-service.com" &

# 资源监控
./resource_monitor.sh &
