#include "cloud_streamer.h"
#include <signal.h>
#include <getopt.h>
#include <iostream>

// 全局变量
std::unique_ptr<CloudStreamer> g_cloud_streamer;

// 信号处理函数
void signal_handler(int signal) {
    switch (signal) {
        case SIGINT:
        case SIGTERM:
            LOG_I("Received termination signal, stopping...");
            if (g_cloud_streamer) {
                g_cloud_streamer->stop();
            }
            break;
        default:
            break;
    }
}

void print_usage(const char* program_name) {
    std::cout << "Usage: " << program_name << " [OPTIONS]\n"
              << "Options:\n"
              << "  -t, --type TYPE       Stream type (webrtc|rtmp)\n"
              << "  -u, --url URL         Stream URL or signaling server\n"
              << "  -v, --verbose         Enable verbose logging\n"
              << "  --help                Show this help message\n";
}

int main(int argc, char* argv[]) {
    // 设置日志级别
    Logger::set_level(LOG_INFO);
    
    // 默认配置
    StreamConfig config;
    config.type = StreamConfig::RTMP;
    config.url = "";
    
    // 解析命令行参数
    static struct option long_options[] = {
        {"type", required_argument, 0, 't'},
        {"url", required_argument, 0, 'u'},
        {"verbose", no_argument, 0, 'v'},
        {"help", no_argument, 0, 1},
        {0, 0, 0, 0}
    };
    
    int c;
    while ((c = getopt_long(argc, argv, "t:u:v", long_options, nullptr)) != -1) {
        switch (c) {
            case 't':
                if (strcmp(optarg, "webrtc") == 0) {
                    config.type = StreamConfig::WEBRTC;
                } else if (strcmp(optarg, "rtmp") == 0) {
                    config.type = StreamConfig::RTMP;
                } else {
                    std::cerr << "Invalid stream type: " << optarg << std::endl;
                    return 1;
                }
                break;
            case 'u':
                config.url = optarg;
                break;
            case 'v':
                Logger::set_level(LOG_DEBUG);
                break;
            case 1:
                print_usage(argv[0]);
                return 0;
            default:
                print_usage(argv[0]);
                return 1;
        }
    }
    
    // 验证配置
    if (config.url.empty()) {
        std::cerr << "Stream URL is required" << std::endl;
        return 1;
    }
    
    // 设置信号处理
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);
    
    LOG_I("Starting cloud streamer service...");
    LOG_I("Type: %s, URL: %s", 
          (config.type == StreamConfig::WEBRTC) ? "WebRTC" : "RTMP",
          config.url.c_str());
    
    try {
        // 创建并初始化服务
        g_cloud_streamer = std::make_unique<CloudStreamer>();
        if (!g_cloud_streamer->init(config)) {
            LOG_E("Failed to initialize cloud streamer");
            return 1;
        }
        
        // 启动服务
        g_cloud_streamer->start();
        
        // 主循环 - 定期输出统计信息
        while (true) {
            CloudStreamer::Stats stats;
            std::this_thread::sleep_for(std::chrono::seconds(10));
            
            g_cloud_streamer->get_stats(stats);
            LOG_I("Stats - Sent: %lu, Dropped: %lu, FPS: %.1f, "
                  "Bitrate: %.1f kbps, CPU: %.1f%%",
                  stats.frames_sent, stats.frames_dropped, stats.fps,
                  stats.bitrate_kbps, stats.cpu_usage);
        }
        
    } catch (const std::exception& e) {
        LOG_E("Exception in main: %s", e.what());
        return 1;
    }
    
    LOG_I("Cloud streamer service stopped");
    return 0;
}
