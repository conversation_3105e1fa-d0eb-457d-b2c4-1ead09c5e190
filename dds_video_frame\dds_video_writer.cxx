#include "dds_video_writer.hpp"
#include <fastdds/rtps/transport/shared_mem/SharedMemTransportDescriptor.hpp>
#include <fastdds/rtps/transport/UDPv4TransportDescriptor.hpp>
#include <stdexcept>
#include <thread>

using namespace eprosima::fastdds;
using namespace eprosima::fastdds::video;
using namespace eprosima::fastdds::dds;
using namespace eprosima::fastdds::rtps;

void DDSWriter::on_publication_matched(
        DataWriter* /*writer*/,
        const PublicationMatchedStatus& info)
{
    if (info.current_count_change == 1)
    {
        std::cout << "Publisher matched: " << info.current_count << " matched." << std::endl;
    }
    else if (info.current_count_change == -1)
    {
        std::cout << "Publisher unmatched: matched_ = " << info.current_count << std::endl;
    }
    else
    {
        std::cout << info.current_count_change
                << " is not a valid value for PublicationMatchedStatus current count change" << std::endl;
    }
}

bool DDSWriter::write(const DDSVideoFrame& dds_frame)
{
    if (!writer_) {
        return false;
    }

    try {
        return writer_->write(&dds_frame);
    } catch (const std::exception& e) {
        printf("DDS write exception: %s", e.what());
        return false;
    }
}

bool DDSWriter::init(const std::string& topic_name, int domain_id, int max_samples)
{
    max_samples_ = max_samples;
    try {
        // 创建Shared Memory传输描述符
        DomainParticipantQos pqos = PARTICIPANT_QOS_DEFAULT;;
        pqos.name("VideoService_Writer");
        std::shared_ptr<SharedMemTransportDescriptor> shm_transport_ =
                std::make_shared<SharedMemTransportDescriptor>();
        shm_transport_->segment_size(shm_transport_->max_message_size() * max_samples_);
        pqos.transport().user_transports.push_back(shm_transport_);
        pqos.transport().user_transports.push_back(std::make_shared<UDPv4TransportDescriptor>());
        pqos.transport().use_builtin_transports = false;

        // 创建跨进程参与者
        auto factory = DomainParticipantFactory::get_instance();
        LibrarySettings library_settings;
        library_settings.intraprocess_delivery = IntraprocessDeliveryType::INTRAPROCESS_OFF;
        factory->set_library_settings(library_settings);
        participant_ = factory->create_participant(domain_id, pqos, nullptr, StatusMask::none());
        if (!participant_) {
            printf("Failed to create DDS participant");
            return false;
        }
        
        // 注册类型
        type_.reset(new DDSVideoFramePubSubType());
        type_.register_type(participant_);
        
        // 创建发布者
        PublisherQos pub_qos = PUBLISHER_QOS_DEFAULT;
        participant_->get_default_publisher_qos(pub_qos);
        publisher_ = participant_->create_publisher(pub_qos, nullptr, StatusMask::none());
        if (publisher_ == nullptr)
        {
            printf("Publisher initialization failed");
            return false;
        }

        // 创建主题
        TopicQos topic_qos = TOPIC_QOS_DEFAULT;
        participant_->get_default_topic_qos(topic_qos);
        topic_ = participant_->create_topic(topic_name, type_.get_type_name(), topic_qos);
        if (!topic_) {
            printf("Failed to create DDS topic: %s", topic_name.c_str());
            return false;
        }
        
        // 创建写入器
        DataWriterQos writer_qos = DATAWRITER_QOS_DEFAULT;
        publisher_->get_default_datawriter_qos(writer_qos);
        writer_qos.reliability().kind = ReliabilityQosPolicyKind::RELIABLE_RELIABILITY_QOS;
        writer_qos.durability().kind = DurabilityQosPolicyKind::TRANSIENT_LOCAL_DURABILITY_QOS;
        writer_qos.history().kind = HistoryQosPolicyKind::KEEP_LAST_HISTORY_QOS;
        writer_qos.history().depth = max_samples_;
        writer_qos.resource_limits().max_samples_per_instance = max_samples_;
        writer_qos.resource_limits().max_samples = writer_qos.resource_limits().max_instances * max_samples_;
        writer_qos.data_sharing().automatic();
        
        writer_ = publisher_->create_datawriter(topic_, writer_qos, this, StatusMask::all());
        if (!writer_) {
            printf("Failed to create DDS writer");
            return false;
        }
        
        printf("DDS Writer initialized for topic: %s", topic_name.c_str());
        return true;
        
    } catch (const std::exception& e) {
        printf("DDS Writer init exception: %s", e.what());
        cleanup();
        return false;
    }
}

void DDSWriter::cleanup()
{
    if (writer_) {
        publisher_->delete_datawriter(writer_);
        writer_ = nullptr;
    }
    if (publisher_) {
        participant_->delete_publisher(publisher_);
        publisher_ = nullptr;
    }
    if (topic_) {
        participant_->delete_topic(topic_);
        topic_ = nullptr;
    }
    if (participant_) {
        DomainParticipantFactory::get_instance()->delete_participant(participant_);
        participant_ = nullptr;
    }
}


