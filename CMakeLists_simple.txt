# Simplified CMakeLists.txt for testing without all dependencies
cmake_minimum_required(VERSION 3.16)

project(VideoService VERSION 1.0.0 LANGUAGES CXX)

# Set C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Project directories
set(PROJECT_INCLUDE_DIR ${CMAKE_CURRENT_SOURCE_DIR}/include)
set(PROJECT_SRC_DIR ${CMAKE_CURRENT_SOURCE_DIR}/src)
set(PROJECT_CONFIG_DIR ${CMAKE_CURRENT_SOURCE_DIR}/config)

# Include directories
include_directories(${PROJECT_INCLUDE_DIR})

# Try to find FFmpeg (optional for testing)
find_package(PkgConfig)
if(PkgConfig_FOUND)
    pkg_check_modules(FFMPEG libavformat libavcodec libavutil)
    if(FFMPEG_FOUND)
        message(STATUS "FFmpeg found: ${FFMPEG_VERSION}")
        include_directories(${FFMPEG_INCLUDE_DIRS})
        link_directories(${FFMPEG_LIBRARY_DIRS})
        add_definitions(-DHAVE_FFMPEG)
    else()
        message(WARNING "FFmpeg not found - RTSP client will not work")
    endif()
else()
    message(WARNING "pkg-config not found - cannot detect FFmpeg")
endif()

# Try to find GStreamer (optional for testing)
if(PkgConfig_FOUND)
    pkg_check_modules(GSTREAMER gstreamer-1.0 gstreamer-app-1.0 gstreamer-video-1.0)
    if(GSTREAMER_FOUND)
        message(STATUS "GStreamer found: ${GSTREAMER_VERSION}")
        include_directories(${GSTREAMER_INCLUDE_DIRS})
        link_directories(${GSTREAMER_LIBRARY_DIRS})
        add_definitions(-DHAVE_GSTREAMER)
    else()
        message(WARNING "GStreamer not found - video converter will not work")
    endif()
endif()

# Common libraries (minimal set)
set(COMMON_LIBS)
if(FFMPEG_FOUND)
    list(APPEND COMMON_LIBS ${FFMPEG_LIBRARIES})
endif()
if(GSTREAMER_FOUND)
    list(APPEND COMMON_LIBS ${GSTREAMER_LIBRARIES})
endif()

# Add threading support
find_package(Threads REQUIRED)
list(APPEND COMMON_LIBS Threads::Threads)

# Build only if we have at least one media framework
if(FFMPEG_FOUND OR GSTREAMER_FOUND)
    # Video capture service
    add_executable(video_capture_main
        ${PROJECT_SRC_DIR}/video_capture_main.cpp
    )
    target_link_libraries(video_capture_main ${COMMON_LIBS})

    # Video converter service  
    add_executable(video_converter_main
        ${PROJECT_SRC_DIR}/video_converter_main.cpp
    )
    target_link_libraries(video_converter_main ${COMMON_LIBS})

    # AI processor service
    add_executable(ai_processor_main
        ${PROJECT_SRC_DIR}/ai_processor_main.cpp
    )
    target_link_libraries(ai_processor_main ${COMMON_LIBS})

    # Cloud streamer service
    add_executable(cloud_streamer_main
        ${PROJECT_SRC_DIR}/cloud_streamer_main.cpp
    )
    target_link_libraries(cloud_streamer_main ${COMMON_LIBS})

    message(STATUS "All executables will be built")
else()
    message(WARNING "No media frameworks found - cannot build executables")
    message(STATUS "Please install FFmpeg or GStreamer development libraries")
endif()

# Configuration summary
message(STATUS "Video Service Configuration Summary:")
message(STATUS "  Build type: ${CMAKE_BUILD_TYPE}")
message(STATUS "  C++ standard: ${CMAKE_CXX_STANDARD}")
message(STATUS "  FFmpeg found: ${FFMPEG_FOUND}")
message(STATUS "  GStreamer found: ${GSTREAMER_FOUND}")
