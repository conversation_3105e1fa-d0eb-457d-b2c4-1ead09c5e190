#!/bin/bash

# Video Service Test Script
# Comprehensive testing for all service components

set -e

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "${SCRIPT_DIR}/.." && pwd)"
TEST_LOG_DIR="/tmp/video_service_test"
TEST_CONFIG="${SCRIPT_DIR}/test_config.json"

# Test video file (create if not exists)
TEST_VIDEO="/tmp/test_video.mp4"

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Test counters
TESTS_TOTAL=0
TESTS_PASSED=0
TESTS_FAILED=0

# Logging
log() {
    echo -e "${BLUE}[$(date '+%H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" >&2
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Test framework
run_test() {
    local test_name="$1"
    local test_function="$2"
    
    TESTS_TOTAL=$((TESTS_TOTAL + 1))
    
    log "Running test: $test_name"
    
    if $test_function; then
        success "✓ $test_name"
        TESTS_PASSED=$((TESTS_PASSED + 1))
        return 0
    else
        error "✗ $test_name"
        TESTS_FAILED=$((TESTS_FAILED + 1))
        return 1
    fi
}

# Setup test environment
setup_test_env() {
    log "Setting up test environment..."
    
    # Create test directories
    mkdir -p "$TEST_LOG_DIR"
    
    # Create test video file if it doesn't exist
    if [[ ! -f "$TEST_VIDEO" ]]; then
        log "Creating test video file..."
        ffmpeg -f lavfi -i testsrc=duration=10:size=1280x720:rate=30 \
               -c:v libx264 -preset fast -crf 23 \
               "$TEST_VIDEO" -y >/dev/null 2>&1 || {
            warning "Failed to create test video, some tests may fail"
        }
    fi
    
    # Create test configuration
    cat > "$TEST_CONFIG" << EOF
{
  "video_capture": {
    "source_type": "v4l2",
    "device": "/dev/video0",
    "width": 640,
    "height": 480,
    "fps": 15,
    "use_dma": false
  },
  "ai_processor": {
    "engine_type": "onnx",
    "model_path": "/tmp/dummy_model.onnx",
    "confidence_threshold": 0.5
  },
  "cloud_streamer": {
    "type": "rtmp",
    "rtmp_url": "rtmp://localhost/live/test"
  }
}
EOF
}

# Cleanup test environment
cleanup_test_env() {
    log "Cleaning up test environment..."
    rm -rf "$TEST_LOG_DIR"
    rm -f "$TEST_CONFIG"
    
    # Kill any test processes
    pkill -f "video_.*_main.*test" 2>/dev/null || true
}

# Test: Check dependencies
test_dependencies() {
    local missing_deps=()
    
    # Check FFmpeg
    if ! command -v ffmpeg >/dev/null 2>&1; then
        missing_deps+=("ffmpeg")
    fi
    
    # Check GStreamer
    if ! pkg-config --exists gstreamer-1.0; then
        missing_deps+=("gstreamer")
    fi
    
    # Check Fast DDS headers
    if [[ ! -f /usr/include/fastdds/dds/DomainParticipant.hpp ]]; then
        missing_deps+=("fastdds")
    fi
    
    if [[ ${#missing_deps[@]} -eq 0 ]]; then
        return 0
    else
        error "Missing dependencies: ${missing_deps[*]}"
        return 1
    fi
}

# Test: Build system
test_build_system() {
    local build_dir="/tmp/video_service_build_test"

    # Test CMake build
    if command -v cmake >/dev/null 2>&1; then
        mkdir -p "$build_dir"
        cd "$build_dir"

        if cmake "$PROJECT_ROOT" >/dev/null 2>&1; then
            cd "$SCRIPT_DIR"
            rm -rf "$build_dir"
            return 0
        else
            cd "$SCRIPT_DIR"
            rm -rf "$build_dir"
            return 1
        fi
    fi

    # Test Makefile
    cd "$PROJECT_ROOT"
    if make -n >/dev/null 2>&1; then
        cd "$SCRIPT_DIR"
        return 0
    else
        cd "$SCRIPT_DIR"
        return 1
    fi
}

# Test: Video capture functionality
test_video_capture() {
    local test_output="${TEST_LOG_DIR}/capture_test.log"
    
    # Test with dummy device (if available)
    if [[ -c /dev/video0 ]]; then
        timeout 5s ./video_capture_main \
            --source v4l2 --device /dev/video0 \
            --width 640 --height 480 --fps 5 \
            --no-dma > "$test_output" 2>&1 || true
        
        # Check if capture started successfully
        if grep -q "Video capture service started" "$test_output"; then
            return 0
        fi
    fi
    
    # If no real device, test RTSP with test file
    if [[ -f "$TEST_VIDEO" ]]; then
        # Start a simple RTSP server with test video (background)
        ffmpeg -re -stream_loop -1 -i "$TEST_VIDEO" \
               -c copy -f rtsp rtsp://localhost:8554/test \
               >/dev/null 2>&1 &
        local ffmpeg_pid=$!
        
        sleep 2
        
        # Test RTSP capture
        timeout 5s ./video_capture_main \
            --source rtsp --url rtsp://localhost:8554/test \
            > "$test_output" 2>&1 || true
        
        kill $ffmpeg_pid 2>/dev/null || true
        
        if grep -q "RTSP client connected" "$test_output"; then
            return 0
        fi
    fi
    
    return 1
}

# Test: Video converter functionality
test_video_converter() {
    local test_output="${TEST_LOG_DIR}/converter_test.log"
    
    # Start converter in test mode
    timeout 5s ./video_converter_main --verbose > "$test_output" 2>&1 || true
    
    # Check if converter initialized
    if grep -q "Video converter initialized" "$test_output"; then
        return 0
    fi
    
    return 1
}

# Test: AI processor functionality
test_ai_processor() {
    local test_output="${TEST_LOG_DIR}/ai_test.log"
    local dummy_model="/tmp/dummy_model.onnx"
    
    # Create a dummy ONNX model file (empty file for testing)
    touch "$dummy_model"
    
    # Test AI processor initialization
    timeout 5s ./ai_processor_main \
        --engine onnx --model "$dummy_model" \
        --confidence 0.5 > "$test_output" 2>&1 || true
    
    rm -f "$dummy_model"
    
    # Check if AI processor started (even if model loading failed)
    if grep -q "AI processor" "$test_output"; then
        return 0
    fi
    
    return 1
}

# Test: Cloud streamer functionality
test_cloud_streamer() {
    local test_output="${TEST_LOG_DIR}/streamer_test.log"
    
    # Test RTMP streamer initialization
    timeout 5s ./cloud_streamer_main \
        --type rtmp --url rtmp://localhost/live/test \
        > "$test_output" 2>&1 || true
    
    # Check if streamer initialized
    if grep -q "Cloud streamer initialized" "$test_output"; then
        return 0
    fi
    
    return 1
}

# Test: DDS communication
test_dds_communication() {
    # Check if DDS shared memory can be created
    local test_shm="/tmp/dds_test_shm"
    
    if mount -t tmpfs -o size=1m tmpfs "$test_shm" 2>/dev/null; then
        umount "$test_shm" 2>/dev/null
        rmdir "$test_shm" 2>/dev/null
        return 0
    fi
    
    return 1
}

# Test: Service manager script
test_service_manager() {
    local manager_script="${SCRIPT_DIR}/video_service_manager.sh"
    
    if [[ ! -f "$manager_script" ]]; then
        return 1
    fi
    
    # Test script syntax
    if bash -n "$manager_script"; then
        return 0
    fi
    
    return 1
}

# Test: Configuration file parsing
test_configuration() {
    if [[ ! -f "$TEST_CONFIG" ]]; then
        return 1
    fi
    
    # Test JSON syntax
    if command -v jq >/dev/null 2>&1; then
        if jq empty "$TEST_CONFIG" >/dev/null 2>&1; then
            return 0
        fi
    else
        # Basic JSON validation without jq
        if python3 -c "import json; json.load(open('$TEST_CONFIG'))" 2>/dev/null; then
            return 0
        fi
    fi
    
    return 1
}

# Performance test
test_performance() {
    local start_time=$(date +%s)
    
    # Run a quick performance test
    timeout 10s ./video_capture_main \
        --source v4l2 --device /dev/video0 \
        --width 320 --height 240 --fps 10 \
        --no-dma >/dev/null 2>&1 || true
    
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    
    # Should complete within reasonable time
    if [[ $duration -le 15 ]]; then
        return 0
    fi
    
    return 1
}

# Integration test
test_integration() {
    local test_duration=10
    
    log "Running integration test for ${test_duration}s..."
    
    # Start all services in background
    ./video_capture_main --source v4l2 --device /dev/video0 --width 320 --height 240 --fps 5 --no-dma >/dev/null 2>&1 &
    local capture_pid=$!
    
    sleep 2
    
    ./video_converter_main >/dev/null 2>&1 &
    local converter_pid=$!
    
    sleep 2
    
    ./ai_processor_main --engine onnx --model /tmp/dummy.onnx >/dev/null 2>&1 &
    local ai_pid=$!
    
    sleep 2
    
    ./cloud_streamer_main --type rtmp --url rtmp://localhost/live/test >/dev/null 2>&1 &
    local streamer_pid=$!
    
    # Let them run for a while
    sleep $test_duration
    
    # Check if processes are still running
    local running_count=0
    for pid in $capture_pid $converter_pid $ai_pid $streamer_pid; do
        if kill -0 $pid 2>/dev/null; then
            running_count=$((running_count + 1))
        fi
        kill $pid 2>/dev/null || true
    done
    
    # At least 2 services should have stayed running
    if [[ $running_count -ge 2 ]]; then
        return 0
    fi
    
    return 1
}

# Run all tests
run_all_tests() {
    log "Starting Video Service Test Suite"
    echo "=================================="
    
    run_test "Dependencies Check" test_dependencies
    run_test "Build System" test_build_system
    run_test "Configuration Parsing" test_configuration
    run_test "Service Manager Script" test_service_manager
    run_test "DDS Communication" test_dds_communication
    run_test "Video Capture" test_video_capture
    run_test "Video Converter" test_video_converter
    run_test "AI Processor" test_ai_processor
    run_test "Cloud Streamer" test_cloud_streamer
    run_test "Performance" test_performance
    
    # Integration test (optional, may fail in test environment)
    if [[ "${RUN_INTEGRATION_TEST:-}" == "1" ]]; then
        run_test "Integration Test" test_integration
    fi
    
    echo
    echo "=================================="
    log "Test Results:"
    success "Passed: $TESTS_PASSED"
    if [[ $TESTS_FAILED -gt 0 ]]; then
        error "Failed: $TESTS_FAILED"
    else
        success "Failed: $TESTS_FAILED"
    fi
    log "Total: $TESTS_TOTAL"
    
    if [[ $TESTS_FAILED -eq 0 ]]; then
        success "All tests passed!"
        return 0
    else
        error "Some tests failed!"
        return 1
    fi
}

# Main function
main() {
    case "${1:-all}" in
        all)
            setup_test_env
            trap cleanup_test_env EXIT
            run_all_tests
            ;;
        deps)
            run_test "Dependencies Check" test_dependencies
            ;;
        build)
            run_test "Build System" test_build_system
            ;;
        capture)
            setup_test_env
            trap cleanup_test_env EXIT
            run_test "Video Capture" test_video_capture
            ;;
        converter)
            setup_test_env
            trap cleanup_test_env EXIT
            run_test "Video Converter" test_video_converter
            ;;
        ai)
            setup_test_env
            trap cleanup_test_env EXIT
            run_test "AI Processor" test_ai_processor
            ;;
        streamer)
            setup_test_env
            trap cleanup_test_env EXIT
            run_test "Cloud Streamer" test_cloud_streamer
            ;;
        integration)
            setup_test_env
            trap cleanup_test_env EXIT
            RUN_INTEGRATION_TEST=1 run_test "Integration Test" test_integration
            ;;
        *)
            echo "Usage: $0 [all|deps|build|capture|converter|ai|streamer|integration]"
            echo ""
            echo "Test categories:"
            echo "  all         - Run all tests (default)"
            echo "  deps        - Check dependencies only"
            echo "  build       - Test build system only"
            echo "  capture     - Test video capture only"
            echo "  converter   - Test video converter only"
            echo "  ai          - Test AI processor only"
            echo "  streamer    - Test cloud streamer only"
            echo "  integration - Run integration test only"
            exit 1
            ;;
    esac
}

main "$@"
