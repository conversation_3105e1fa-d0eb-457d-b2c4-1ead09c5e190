#ifndef DDS_INTERFACE_H
#define DDS_INTERFACE_H

#include "common.h"
#include <fastdds/dds/domain/DomainParticipant.hpp>
#include <fastdds/dds/domain/DomainParticipantFactory.hpp>
#include <fastdds/dds/topic/TypeSupport.hpp>
#include <fastdds/dds/topic/Topic.hpp>
#include <fastdds/dds/publisher/Publisher.hpp>
#include <fastdds/dds/publisher/DataWriter.hpp>
#include <fastdds/dds/subscriber/Subscriber.hpp>
#include <fastdds/dds/subscriber/DataReader.hpp>
#include <fastdds/dds/subscriber/SampleInfo.hpp>

using namespace eprosima::fastdds::dds;

// DDS VideoFrame类型定义
struct DDSVideoFrame {
    uint64_t frame_id;
    uint64_t timestamp;
    uint16_t width;
    uint16_t height;
    uint8_t format;
    int32_t dma_fd;
    uint32_t data_length;
    std::vector<uint8_t> data;
    uint8_t source_type;
    bool is_keyframe;
    
    // 从Frame转换
    static DDSVideoFrame from_frame(const Frame& frame) {
        DDSVideoFrame dds_frame;
        dds_frame.frame_id = frame.frame_id;
        dds_frame.timestamp = frame.timestamp;
        dds_frame.width = frame.width;
        dds_frame.height = frame.height;
        dds_frame.format = static_cast<uint8_t>(frame.format);
        dds_frame.dma_fd = frame.dma_fd;
        dds_frame.data_length = frame.data_length;
        dds_frame.data = frame.data;
        dds_frame.source_type = frame.source_type;
        dds_frame.is_keyframe = frame.is_keyframe;
        return dds_frame;
    }
    
    // 转换为Frame
    Frame to_frame() const {
        Frame frame;
        frame.frame_id = frame_id;
        frame.timestamp = timestamp;
        frame.width = width;
        frame.height = height;
        frame.format = static_cast<FrameFormat>(format);
        frame.dma_fd = dma_fd;
        frame.data_length = data_length;
        frame.data = data;
        frame.source_type = source_type;
        frame.is_keyframe = is_keyframe;
        frame.valid = true;
        return frame;
    }
};

// DDS Writer封装
class DDSWriter {
private:
    DomainParticipant* participant_;
    Publisher* publisher_;
    Topic* topic_;
    DataWriter* writer_;
    TypeSupport type_;
    
public:
    DDSWriter() : participant_(nullptr), publisher_(nullptr), 
                  topic_(nullptr), writer_(nullptr) {}
    
    ~DDSWriter() {
        cleanup();
    }
    
    bool init(const std::string& topic_name, int domain_id = 0) {
        try {
            // 创建参与者
            DomainParticipantQos pqos;
            pqos.name("VideoService_Writer");
            participant_ = DomainParticipantFactory::get_instance()->create_participant(
                domain_id, pqos);
            if (!participant_) {
                LOG_E("Failed to create DDS participant");
                return false;
            }
            
            // 注册类型
            type_.reset(new DDSVideoFramePubSubType());
            type_.register_type(participant_);
            
            // 创建主题
            topic_ = participant_->create_topic(topic_name, type_.get_type_name(), TOPIC_QOS_DEFAULT);
            if (!topic_) {
                LOG_E("Failed to create DDS topic: %s", topic_name.c_str());
                return false;
            }
            
            // 创建发布者
            publisher_ = participant_->create_publisher(PUBLISHER_QOS_DEFAULT);
            if (!publisher_) {
                LOG_E("Failed to create DDS publisher");
                return false;
            }
            
            // 创建写入器
            DataWriterQos wqos;
            wqos.reliability().kind = RELIABLE_RELIABILITY_QOS;
            wqos.durability().kind = VOLATILE_DURABILITY_QOS;
            wqos.history().kind = KEEP_LAST_HISTORY_QOS;
            wqos.history().depth = 5;
            
            writer_ = publisher_->create_datawriter(topic_, wqos);
            if (!writer_) {
                LOG_E("Failed to create DDS writer");
                return false;
            }
            
            LOG_I("DDS Writer initialized for topic: %s", topic_name.c_str());
            return true;
            
        } catch (const std::exception& e) {
            LOG_E("DDS Writer init exception: %s", e.what());
            cleanup();
            return false;
        }
    }
    
    bool write(const Frame& frame) {
        if (!writer_) {
            return false;
        }
        
        try {
            DDSVideoFrame dds_frame = DDSVideoFrame::from_frame(frame);
            return writer_->write(&dds_frame);
        } catch (const std::exception& e) {
            LOG_E("DDS write exception: %s", e.what());
            return false;
        }
    }
    
private:
    void cleanup() {
        if (writer_) {
            publisher_->delete_datawriter(writer_);
            writer_ = nullptr;
        }
        if (publisher_) {
            participant_->delete_publisher(publisher_);
            publisher_ = nullptr;
        }
        if (topic_) {
            participant_->delete_topic(topic_);
            topic_ = nullptr;
        }
        if (participant_) {
            DomainParticipantFactory::get_instance()->delete_participant(participant_);
            participant_ = nullptr;
        }
    }
};

// DDS Reader封装
class DDSReader {
private:
    DomainParticipant* participant_;
    Subscriber* subscriber_;
    Topic* topic_;
    DataReader* reader_;
    TypeSupport type_;
    
public:
    DDSReader() : participant_(nullptr), subscriber_(nullptr), 
                  topic_(nullptr), reader_(nullptr) {}
    
    ~DDSReader() {
        cleanup();
    }
    
    bool init(const std::string& topic_name, int domain_id = 0) {
        try {
            // 创建参与者
            DomainParticipantQos pqos;
            pqos.name("VideoService_Reader");
            participant_ = DomainParticipantFactory::get_instance()->create_participant(
                domain_id, pqos);
            if (!participant_) {
                LOG_E("Failed to create DDS participant");
                return false;
            }
            
            // 注册类型
            type_.reset(new DDSVideoFramePubSubType());
            type_.register_type(participant_);
            
            // 创建主题
            topic_ = participant_->create_topic(topic_name, type_.get_type_name(), TOPIC_QOS_DEFAULT);
            if (!topic_) {
                LOG_E("Failed to create DDS topic: %s", topic_name.c_str());
                return false;
            }
            
            // 创建订阅者
            subscriber_ = participant_->create_subscriber(SUBSCRIBER_QOS_DEFAULT);
            if (!subscriber_) {
                LOG_E("Failed to create DDS subscriber");
                return false;
            }
            
            // 创建读取器
            DataReaderQos rqos;
            rqos.reliability().kind = RELIABLE_RELIABILITY_QOS;
            rqos.durability().kind = VOLATILE_DURABILITY_QOS;
            rqos.history().kind = KEEP_LAST_HISTORY_QOS;
            rqos.history().depth = 5;
            
            reader_ = subscriber_->create_datareader(topic_, rqos);
            if (!reader_) {
                LOG_E("Failed to create DDS reader");
                return false;
            }
            
            LOG_I("DDS Reader initialized for topic: %s", topic_name.c_str());
            return true;
            
        } catch (const std::exception& e) {
            LOG_E("DDS Reader init exception: %s", e.what());
            cleanup();
            return false;
        }
    }
    
    bool read(Frame& frame, int timeout_ms = 100) {
        if (!reader_) {
            return false;
        }
        
        try {
            SampleInfoSeq info_seq;
            DDSVideoFrameSeq data_seq;
            
            ReturnCode_t ret = reader_->take(data_seq, info_seq, 1, ANY_SAMPLE_STATE, 
                                           ANY_VIEW_STATE, ANY_INSTANCE_STATE);
            
            if (ret == ReturnCode_t::RETCODE_OK && data_seq.length() > 0) {
                if (info_seq[0].valid_data) {
                    frame = data_seq[0].to_frame();
                    reader_->return_loan(data_seq, info_seq);
                    return true;
                }
            }
            
            reader_->return_loan(data_seq, info_seq);
            return false;
            
        } catch (const std::exception& e) {
            LOG_E("DDS read exception: %s", e.what());
            return false;
        }
    }
    
    bool wait_for_data(int timeout_ms = 1000) {
        if (!reader_) {
            return false;
        }
        
        // 简单的轮询实现
        auto start = std::chrono::steady_clock::now();
        while (std::chrono::duration_cast<std::chrono::milliseconds>(
                   std::chrono::steady_clock::now() - start).count() < timeout_ms) {
            
            SampleInfoSeq info_seq;
            DDSVideoFrameSeq data_seq;
            
            ReturnCode_t ret = reader_->read(data_seq, info_seq, 1, ANY_SAMPLE_STATE, 
                                           ANY_VIEW_STATE, ANY_INSTANCE_STATE);
            
            if (ret == ReturnCode_t::RETCODE_OK && data_seq.length() > 0) {
                reader_->return_loan(data_seq, info_seq);
                return true;
            }
            
            reader_->return_loan(data_seq, info_seq);
            std::this_thread::sleep_for(std::chrono::milliseconds(10));
        }
        
        return false;
    }
    
private:
    void cleanup() {
        if (reader_) {
            subscriber_->delete_datareader(reader_);
            reader_ = nullptr;
        }
        if (subscriber_) {
            participant_->delete_subscriber(subscriber_);
            subscriber_ = nullptr;
        }
        if (topic_) {
            participant_->delete_topic(topic_);
            topic_ = nullptr;
        }
        if (participant_) {
            DomainParticipantFactory::get_instance()->delete_participant(participant_);
            participant_ = nullptr;
        }
    }
};

#endif // DDS_INTERFACE_H
