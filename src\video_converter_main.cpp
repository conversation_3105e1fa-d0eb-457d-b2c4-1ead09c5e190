#include "video_converter.h"
#include <signal.h>
#include <getopt.h>
#include <iostream>

// 全局变量
std::unique_ptr<VideoConverter> g_converter;

// 信号处理函数
void signal_handler(int signal) {
    switch (signal) {
        case SIGINT:
        case SIGTERM:
            LOG_I("Received termination signal, stopping...");
            if (g_converter) {
                g_converter->stop();
            }
            break;
        case SIGUSR1:
            if (g_converter) {
                g_converter->handle_signal(signal);
            }
            break;
        default:
            break;
    }
}

void print_usage(const char* program_name) {
    std::cout << "Usage: " << program_name << " [OPTIONS]\n"
              << "Options:\n"
              << "  -v, --verbose         Enable verbose logging\n"
              << "  --help                Show this help message\n";
}

int main(int argc, char* argv[]) {
    // 设置日志级别
    Logger::set_level(LOG_INFO);
    
    // 解析命令行参数
    static struct option long_options[] = {
        {"verbose", no_argument, 0, 'v'},
        {"help", no_argument, 0, 1},
        {0, 0, 0, 0}
    };
    
    int c;
    while ((c = getopt_long(argc, argv, "v", long_options, nullptr)) != -1) {
        switch (c) {
            case 'v':
                Logger::set_level(LOG_DEBUG);
                break;
            case 1:
                print_usage(argv[0]);
                return 0;
            default:
                print_usage(argv[0]);
                return 1;
        }
    }
    
    // 设置信号处理
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);
    signal(SIGUSR1, signal_handler);
    
    LOG_I("Starting video converter service...");
    
    try {
        // 创建并初始化服务
        g_converter = std::make_unique<VideoConverter>();
        if (!g_converter->init()) {
            LOG_E("Failed to initialize video converter");
            return 1;
        }
        
        // 启动服务
        g_converter->start();
        
        // 主循环 - 定期输出统计信息
        while (true) {
            std::this_thread::sleep_for(std::chrono::seconds(15));
            
            auto stats = g_converter->get_stats();
            LOG_I("Stats - Processed: %lu, Dropped: %lu, CPU: %.1f%%",
                  stats.frames_processed, stats.frames_dropped, stats.cpu_usage);
        }
        
    } catch (const std::exception& e) {
        LOG_E("Exception in main: %s", e.what());
        return 1;
    }
    
    LOG_I("Video converter service stopped");
    return 0;
}
