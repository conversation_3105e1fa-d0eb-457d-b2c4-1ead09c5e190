#!/bin/bash

# Video Service Debug Tool
# Comprehensive debugging utilities for video service components

set -e

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
DEBUG_LOG_DIR="/tmp/video_service_debug"
PID_DIR="/var/run/video_service"

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

# Logging
log() {
    echo -e "${BLUE}[$(date '+%H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" >&2
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

info() {
    echo -e "${CYAN}[INFO]${NC} $1"
}

# Setup debug environment
setup_debug() {
    mkdir -p "$DEBUG_LOG_DIR"
    
    # Set debug environment variables
    export GST_DEBUG=3
    export GST_DEBUG_FILE="${DEBUG_LOG_DIR}/gstreamer.log"
    export FASTDDS_LOG_LEVEL=Info
    export FASTDDS_LOG_FILE="${DEBUG_LOG_DIR}/fastdds.log"
    
    log "Debug environment setup complete"
    log "Debug logs will be saved to: $DEBUG_LOG_DIR"
}

# Analyze system resources
analyze_system() {
    log "System Resource Analysis"
    echo "========================"
    
    # CPU information
    echo "CPU Information:"
    lscpu | grep -E "(Model name|CPU\(s\)|Thread|Core|Socket)"
    echo
    
    # Memory information
    echo "Memory Information:"
    free -h
    echo
    
    # Disk space
    echo "Disk Space:"
    df -h | grep -E "(Filesystem|/dev/|tmpfs.*dds)"
    echo
    
    # Video devices
    echo "Video Devices:"
    if ls /dev/video* >/dev/null 2>&1; then
        for device in /dev/video*; do
            if [[ -c "$device" ]]; then
                echo "  $device: $(v4l2-ctl --device=$device --info 2>/dev/null | grep 'Card type' | cut -d: -f2 | xargs || echo 'Unknown')"
            fi
        done
    else
        echo "  No video devices found"
    fi
    echo
    
    # Network interfaces
    echo "Network Interfaces:"
    ip addr show | grep -E "(inet |inet6 )" | awk '{print "  " $2 " (" $NF ")"}'
    echo
}

# Analyze DDS communication
analyze_dds() {
    log "DDS Communication Analysis"
    echo "=========================="
    
    local dds_shm="/tmp/dds_shm"
    
    if [[ -d "$dds_shm" ]]; then
        echo "DDS Shared Memory:"
        du -sh "$dds_shm"
        echo
        
        echo "DDS Topics:"
        find "$dds_shm" -type f 2>/dev/null | head -10 | while read -r file; do
            echo "  $(basename "$file")"
        done
        echo
    else
        warning "DDS shared memory not found at $dds_shm"
    fi
    
    # Check DDS processes
    echo "DDS Related Processes:"
    ps aux | grep -E "(fastdds|dds)" | grep -v grep || echo "  No DDS processes found"
    echo
}

# Analyze video pipeline
analyze_video_pipeline() {
    log "Video Pipeline Analysis"
    echo "======================="
    
    # Check GStreamer plugins
    echo "GStreamer Plugin Status:"
    local plugins=("v4l2" "x264" "rtmp" "webrtc" "app")
    for plugin in "${plugins[@]}"; do
        if gst-inspect-1.0 "$plugin" >/dev/null 2>&1; then
            echo -e "  ${GREEN}✓${NC} $plugin"
        else
            echo -e "  ${RED}✗${NC} $plugin"
        fi
    done
    echo
    
    # Check FFmpeg codecs
    echo "FFmpeg Codec Support:"
    local codecs=("h264" "h265" "vp8" "vp9")
    for codec in "${codecs[@]}"; do
        if ffmpeg -codecs 2>/dev/null | grep -q "$codec"; then
            echo -e "  ${GREEN}✓${NC} $codec"
        else
            echo -e "  ${RED}✗${NC} $codec"
        fi
    done
    echo
}

# Trace service execution
trace_service() {
    local service_name="$1"
    local duration="${2:-10}"
    
    if [[ -z "$service_name" ]]; then
        error "Service name required"
        return 1
    fi
    
    local binary="${SCRIPT_DIR}/${service_name}_main"
    if [[ ! -f "$binary" ]]; then
        error "Service binary not found: $binary"
        return 1
    fi
    
    log "Tracing $service_name for ${duration}s..."
    
    # Start service with strace
    local trace_file="${DEBUG_LOG_DIR}/${service_name}_trace.log"
    local output_file="${DEBUG_LOG_DIR}/${service_name}_output.log"
    
    timeout "$duration" strace -f -t -o "$trace_file" \
        "$binary" --verbose > "$output_file" 2>&1 || true
    
    success "Trace completed. Files:"
    echo "  System calls: $trace_file"
    echo "  Program output: $output_file"
}

# Profile service performance
profile_service() {
    local service_name="$1"
    local duration="${2:-30}"
    
    if [[ -z "$service_name" ]]; then
        error "Service name required"
        return 1
    fi
    
    local pid_file="${PID_DIR}/${service_name}.pid"
    if [[ ! -f "$pid_file" ]]; then
        error "Service $service_name is not running"
        return 1
    fi
    
    local pid=$(cat "$pid_file")
    if ! kill -0 "$pid" 2>/dev/null; then
        error "Service $service_name (PID: $pid) is not running"
        return 1
    fi
    
    log "Profiling $service_name (PID: $pid) for ${duration}s..."
    
    # CPU profiling with perf (if available)
    if command -v perf >/dev/null 2>&1; then
        local perf_file="${DEBUG_LOG_DIR}/${service_name}_perf.data"
        timeout "$duration" perf record -p "$pid" -o "$perf_file" 2>/dev/null || true
        
        if [[ -f "$perf_file" ]]; then
            perf report -i "$perf_file" > "${DEBUG_LOG_DIR}/${service_name}_perf_report.txt" 2>/dev/null || true
            success "Performance profile saved to ${DEBUG_LOG_DIR}/${service_name}_perf_report.txt"
        fi
    fi
    
    # Memory profiling with valgrind (for stopped services)
    info "For memory profiling, restart service with: valgrind --tool=massif ./${service_name}_main"
}

# Capture network traffic
capture_network() {
    local interface="${1:-any}"
    local duration="${2:-30}"
    local filter="${3:-}"
    
    if ! command -v tcpdump >/dev/null 2>&1; then
        error "tcpdump not available"
        return 1
    fi
    
    log "Capturing network traffic on $interface for ${duration}s..."
    
    local capture_file="${DEBUG_LOG_DIR}/network_capture.pcap"
    local tcpdump_args="-i $interface -w $capture_file"
    
    if [[ -n "$filter" ]]; then
        tcpdump_args="$tcpdump_args $filter"
    fi
    
    timeout "$duration" tcpdump $tcpdump_args 2>/dev/null || true
    
    if [[ -f "$capture_file" ]]; then
        success "Network capture saved to $capture_file"
        
        # Basic analysis
        echo "Capture Summary:"
        tcpdump -r "$capture_file" 2>/dev/null | head -20
    fi
}

# Generate debug report
generate_debug_report() {
    local report_file="${DEBUG_LOG_DIR}/debug_report_$(date +%Y%m%d_%H%M%S).txt"
    
    log "Generating comprehensive debug report..."
    
    {
        echo "Video Service Debug Report"
        echo "========================="
        echo "Generated: $(date)"
        echo "Hostname: $(hostname)"
        echo "Kernel: $(uname -r)"
        echo
        
        analyze_system
        analyze_dds
        analyze_video_pipeline
        
        echo "Service Status:"
        echo "==============="
        "${SCRIPT_DIR}/video_service_manager.sh" status 2>/dev/null || echo "Service manager not available"
        echo
        
        echo "Recent Log Entries:"
        echo "==================="
        if [[ -d "/var/log/video_service" ]]; then
            for log_file in /var/log/video_service/*.log; do
                if [[ -f "$log_file" ]]; then
                    echo "--- $(basename "$log_file") ---"
                    tail -20 "$log_file" 2>/dev/null || echo "Cannot read log file"
                    echo
                fi
            done
        fi
        
        echo "Process Information:"
        echo "==================="
        ps aux | grep -E "(video_|ai_|cloud_)" | grep -v grep || echo "No video service processes found"
        echo
        
        echo "System Resources:"
        echo "================="
        top -bn1 | head -20
        echo
        
    } > "$report_file"
    
    success "Debug report saved to: $report_file"
}

# Interactive debugging session
interactive_debug() {
    log "Starting interactive debugging session"
    echo "======================================"
    
    while true; do
        echo
        echo "Debug Options:"
        echo "1. Analyze system resources"
        echo "2. Analyze DDS communication"
        echo "3. Analyze video pipeline"
        echo "4. Trace service execution"
        echo "5. Profile service performance"
        echo "6. Capture network traffic"
        echo "7. Generate debug report"
        echo "8. View service logs"
        echo "9. Exit"
        echo
        
        read -p "Select option (1-9): " choice
        
        case $choice in
            1)
                analyze_system
                ;;
            2)
                analyze_dds
                ;;
            3)
                analyze_video_pipeline
                ;;
            4)
                read -p "Service name (video_capture/video_converter/ai_processor/cloud_streamer): " service
                read -p "Duration in seconds [10]: " duration
                trace_service "$service" "${duration:-10}"
                ;;
            5)
                read -p "Service name: " service
                read -p "Duration in seconds [30]: " duration
                profile_service "$service" "${duration:-30}"
                ;;
            6)
                read -p "Network interface [any]: " interface
                read -p "Duration in seconds [30]: " duration
                read -p "Filter (optional): " filter
                capture_network "${interface:-any}" "${duration:-30}" "$filter"
                ;;
            7)
                generate_debug_report
                ;;
            8)
                if [[ -d "/var/log/video_service" ]]; then
                    echo "Available logs:"
                    ls -la /var/log/video_service/
                    read -p "Log file to view: " log_file
                    if [[ -f "/var/log/video_service/$log_file" ]]; then
                        tail -f "/var/log/video_service/$log_file"
                    else
                        error "Log file not found"
                    fi
                else
                    error "Log directory not found"
                fi
                ;;
            9)
                log "Exiting debug session"
                break
                ;;
            *)
                error "Invalid option"
                ;;
        esac
    done
}

# Main function
main() {
    setup_debug
    
    case "${1:-interactive}" in
        system)
            analyze_system
            ;;
        dds)
            analyze_dds
            ;;
        pipeline)
            analyze_video_pipeline
            ;;
        trace)
            trace_service "$2" "$3"
            ;;
        profile)
            profile_service "$2" "$3"
            ;;
        network)
            capture_network "$2" "$3" "$4"
            ;;
        report)
            generate_debug_report
            ;;
        interactive)
            interactive_debug
            ;;
        *)
            echo "Usage: $0 [system|dds|pipeline|trace|profile|network|report|interactive]"
            echo ""
            echo "Commands:"
            echo "  system      - Analyze system resources"
            echo "  dds         - Analyze DDS communication"
            echo "  pipeline    - Analyze video pipeline"
            echo "  trace       - Trace service execution"
            echo "  profile     - Profile service performance"
            echo "  network     - Capture network traffic"
            echo "  report      - Generate comprehensive debug report"
            echo "  interactive - Interactive debugging session (default)"
            exit 1
            ;;
    esac
}

main "$@"
