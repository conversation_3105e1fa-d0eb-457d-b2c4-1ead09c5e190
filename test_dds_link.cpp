// 测试DDS链接问题的简单程序
#include "include/common.h"
#include <iostream>

int main() {
    try {
        std::cout << "Testing DDSVideoWriter creation..." << std::endl;
        
        // 测试DDSVideoWriter的创建和销毁
        {
            DDSVideoWriter writer("test_topic", 3);
            std::cout << "DDSVideoWriter created successfully" << std::endl;
        } // 这里会调用析构函数
        
        std::cout << "DDSVideoWriter destroyed successfully" << std::endl;
        
        std::cout << "Testing DDSVideoReader creation..." << std::endl;
        
        // 测试DDSVideoReader的创建和销毁
        {
            DDSVideoReader reader("test_topic", 3);
            std::cout << "DDSVideoReader created successfully" << std::endl;
        } // 这里会调用析构函数
        
        std::cout << "DDSVideoReader destroyed successfully" << std::endl;
        
        std::cout << "All DDS tests passed!" << std::endl;
        return 0;
        
    } catch (const std::exception& e) {
        std::cerr << "DDS test failed: " << e.what() << std::endl;
        return 1;
    }
}
