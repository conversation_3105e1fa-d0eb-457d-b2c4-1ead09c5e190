<dds xmlns="http://www.eprosima.com/XMLSchemas/fastRTPS_Profiles">
    <!-- 嵌入式发布者配置 -->
    <data_writer profile_name="embedded_video_writer">
        <qos>
            <!-- 可靠性：嵌入式通常需要确定性传输 -->
            <reliability>
                <kind>RELIABLE_RELIABILITY_QOS</kind> <!-- 关键帧必须可靠 -->
                <max_blocking_time>
                    <sec>0</sec>
                    <nanosec>2000000</nanosec> <!-- 2ms阻塞时间 -->
                </max_blocking_time>
            </reliability>
            
            <!-- 发布模式：同步减少内存开销 -->
            <publish_mode>
                <kind>SYNCHRONOUS_PUBLISH_MODE</kind> <!-- 避免异步缓冲 -->
            </publish_mode>
            
            <!-- 严格内存控制 -->
            <resource_limits>
                <max_samples>20</max_samples>       <!-- 仅保留20帧 -->
                <max_instances>3</max_instances>     <!-- 支持3个并发视频流 -->
                <max_samples_per_instance>8</max_samples_per_instance> <!-- 每帧分片数 -->
                <allocated_samples>10</allocated_samples> <!-- 预分配样本 -->
            </resource_limits>
            
            <!-- 历史策略：仅保留最新帧 -->
            <history>
                <kind>KEEP_LAST_HISTORY_QOS</kind>
                <depth>1</depth>  <!-- 只保留最新一帧 -->
            </history>
            
            <!-- 数据共享优化 -->
            <data_sharing>
                <kind>AUTOMATIC</kind>
                <shared_memory_directory>/tmp/shm</shared_memory_directory> <!-- 使用tmpfs -->
            </data_sharing>
            
            <!-- 延长心跳周期 -->
            <liveliness>
                <lease_duration>
                    <sec>3</sec>  <!-- 3秒租期 -->
                </lease_duration>
            </liveliness>
        </qos>
        
        <!-- 内存策略：静态预分配 -->
        <historyMemoryPolicy>PREALLOCATED_STATIC_MEMORY_MODE</historyMemoryPolicy>
    </data_writer>

    <!-- 嵌入式订阅者配置 -->
    <data_reader profile_name="embedded_video_reader">
        <qos>
            <reliability>
                <kind>RELIABLE_RELIABILITY_QOS</kind>
            </reliability>
            <resource_limits>
                <max_samples>20</max_samples>
                <max_instances>3</max_instances>
                <max_samples_per_instance>8</max_samples_per_instance>
            </resource_limits>
            <history>
                <kind>KEEP_LAST_HISTORY_QOS</kind>
                <depth>1</depth>
            </history>
            <data_sharing>
                <kind>AUTOMATIC</kind>
                <shared_memory_directory>/tmp/shm</shared_memory_directory>
            </data_sharing>
            <!-- 单样本读取降低延迟 -->
            <reader_resource_limits>
                <max_samples_per_read>1</max_samples_per_read>
            </reader_resource_limits>
            <!-- 延长响应时间 -->
            <time_based_filter>
                <minimum_separation>
                    <sec>0</sec>
                    <nanosec>10000000</nanosec> <!-- 10ms过滤 -->
                </minimum_separation>
            </time_based_filter>
        </qos>
        <!-- 同步监听确保实时性 -->
        <listener>
            <asynchronous>false</asynchronous>
        </listener>
    </data_reader>

    <!-- 嵌入式域参与者配置 -->
    <participant profile_name="embedded_participant">
        <rtps>
            <!-- 固定大小缓冲区 -->
            <sendBuffers>
                <preallocated_number>4</preallocated_number> <!-- 小型缓冲池 -->
                <dynamic>false</dynamic> <!-- 禁止动态增长 -->
            </sendBuffers>
            
            <!-- 仅使用共享内存传输 -->
            <userTransports>
                <transport_id>SharedMemoryTransport</transport_id>
            </userTransports>
            <useBuiltinTransports>false</useBuiltinTransports>
            
            <!-- 小分片匹配内存页 -->
            <fragment_size>2048</fragment_size>  <!-- 2KB分片 -->
            
            <!-- 简化发现协议 -->
            <builtin>
                <discovery_config>
                    <protocol>SUPER_CLIENT</protocol> <!-- 单一发现服务器 -->
                    <leaseDuration>
                        <sec>5</sec>  <!-- 较短租期 -->
                    </leaseDuration>
                </discovery_config>
                <!-- 关闭非必要服务 -->
                <typelookup_config>
                    <use_client>false</use_client>
                    <use_server>false</use_server>
                </typelookup_config>
                <use_WriterLivelinessProtocol>false</use_WriterLivelinessProtocol>
            </builtin>
            
            <!-- 减少线程数量 -->
            <builtin>
                <metatraffic_external_unicast_locators>false</metatraffic_external_unicast_locators>
            </builtin>
            <port>
                <domainIdGain>0</domainIdGain>
                <participantIdGain>0</participantIdGain>
            </port>
        </rtps>
    </participant>
</dds>

