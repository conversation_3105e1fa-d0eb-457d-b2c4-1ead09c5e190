# RTSP服务器服务指南

## 概述

RTSP服务器服务是视频服务系统的核心组件之一，负责接收来自DDS的视频帧数据，并通过RTSP协议向客户端提供实时视频流。该服务具有高效、灵活、可配置的特点，支持多种视频格式的自动转换和优化。

## 主要特性

### 🚀 高性能特性
- **零拷贝优化**: 最小化内存拷贝操作，降低延迟
- **硬件加速**: 支持NVIDIA GPU硬件编码器 (nvh264enc/nvh265enc)
- **实时处理**: 针对实时流媒体优化的参数配置
- **自适应码率**: 根据网络条件动态调整视频质量

### 🔧 灵活配置
- **可配置DDS Topic**: 支持从任意DDS主题接收视频数据
- **多种输出格式**: 支持H.264/H.265编码，可配置分辨率和帧率
- **统一格式转换**: 自动将各种输入格式转换为标准1280x720@30fps H.264流
- **动态参数调整**: 运行时可调整码率、GOP大小等参数

### 🌐 网络优化
- **多客户端支持**: 可同时服务多个RTSP客户端
- **传输协议优化**: 支持TCP/UDP传输，针对不同网络环境优化
- **缓冲区管理**: 智能缓冲区大小调整，平衡延迟和稳定性

## 系统架构

```
DDS Topic (VideoFrames) 
    ↓
DDSVideoReader
    ↓
VideoFormatConverter (自动格式检测和转换)
    ↓
GStreamer RTSP Server
    ↓
RTSP Clients (VLC, FFplay, etc.)
```

### 核心组件

1. **DDSVideoReader**: 从指定DDS主题读取视频帧
2. **VideoFormatConverter**: 高效视频格式转换器
3. **RTSPMediaFactory**: RTSP媒体工厂，为每个客户端创建流
4. **RTSPServerService**: 主服务类，管理整个RTSP服务器

## 快速开始

### 1. 编译项目

```bash
# 使用CMake
mkdir build && cd build
cmake ..
make rtsp_server

# 或使用Makefile
make rtsp_server_main
```

### 2. 基本使用

```bash
# 使用默认配置启动
./build/rtsp_server_main

# 指定DDS主题和端口
./build/rtsp_server_main --topic VideoFrames --port 8554

# 使用配置文件
./build/rtsp_server_main --config config/rtsp_server.json
```

### 3. 使用启动脚本

```bash
# 启动服务器
./scripts/start_rtsp_server.sh start

# 查看状态
./scripts/start_rtsp_server.sh status

# 测试RTSP流
./scripts/start_rtsp_server.sh test
```

## 配置说明

### 命令行参数

| 参数 | 说明 | 默认值 |
|------|------|--------|
| `--topic` | DDS输入主题名称 | VideoFrames |
| `--address` | 服务器绑定地址 | 0.0.0.0 |
| `--port` | RTSP服务器端口 | 8554 |
| `--mount` | RTSP挂载点 | /stream |
| `--width` | 输出视频宽度 | 1280 |
| `--height` | 输出视频高度 | 720 |
| `--fps` | 输出帧率 | 30 |
| `--bitrate` | 输出码率(bps) | 2000000 |
| `--gop-size` | GOP大小 | 30 |
| `--hw-encoder` | 使用硬件编码器 | 自动检测 |
| `--sw-encoder` | 强制软件编码器 | - |
| `--max-clients` | 最大客户端数 | 10 |
| `--buffer-size` | DDS缓冲区大小 | 5 |
| `--stats-interval` | 统计信息间隔(秒) | 10 |

### 配置文件格式

```json
{
  "rtsp_server": {
    "dds_topic": "VideoFrames",
    "server_address": "0.0.0.0",
    "server_port": 8554,
    "mount_point": "/stream",
    
    "output_video": {
      "width": 1280,
      "height": 720,
      "fps": 30,
      "codec": "H264",
      "bitrate": 2000000,
      "gop_size": 30
    },
    
    "encoder": {
      "use_hardware_encoder": true,
      "fallback_to_software": true
    },
    
    "performance": {
      "zero_copy_mode": true,
      "buffer_size": 5,
      "max_clients": 10
    },
    
    "quality_control": {
      "adaptive_bitrate": true,
      "min_bitrate": 500000,
      "max_bitrate": 5000000
    }
  }
}
```

## 使用示例

### 基本RTSP流服务

```bash
# 启动基本RTSP服务器
./build/rtsp_server_main --topic VideoFrames --port 8554

# 客户端访问
ffplay rtsp://*************:8554/stream
vlc rtsp://*************:8554/stream
```

### 多流配置

```bash
# 主流 - 高质量
./build/rtsp_server_main --topic MainFrames --port 8554 --mount /main --bitrate 5000000

# AI流 - 中等质量
./build/rtsp_server_main --topic AIFrames --port 8555 --mount /ai --width 640 --height 640 --bitrate 1000000

# 云端流 - 低延迟
./build/rtsp_server_main --topic CloudFrames --port 8556 --mount /cloud --hw-encoder
```

### 性能优化配置

```bash
# 低延迟配置
./build/rtsp_server_main \
    --topic VideoFrames \
    --hw-encoder \
    --gop-size 15 \
    --buffer-size 3 \
    --bitrate 3000000

# 高质量配置
./build/rtsp_server_main \
    --topic VideoFrames \
    --width 1920 \
    --height 1080 \
    --fps 60 \
    --bitrate 8000000 \
    --gop-size 60
```

## 客户端连接

### 支持的客户端

- **VLC Media Player**: `vlc rtsp://server:port/mount`
- **FFplay**: `ffplay rtsp://server:port/mount`
- **GStreamer**: `gst-launch-1.0 rtspsrc location=rtsp://server:port/mount ! decodebin ! autovideosink`
- **OpenCV**: 使用VideoCapture类连接RTSP流
- **Web浏览器**: 通过WebRTC网关或HLS转换

### 连接示例

```python
# Python OpenCV示例
import cv2

cap = cv2.VideoCapture('rtsp://*************:8554/stream')
while True:
    ret, frame = cap.read()
    if ret:
        cv2.imshow('RTSP Stream', frame)
        if cv2.waitKey(1) & 0xFF == ord('q'):
            break
cap.release()
cv2.destroyAllWindows()
```

```javascript
// JavaScript WebRTC示例 (需要WebRTC网关)
const video = document.getElementById('video');
const pc = new RTCPeerConnection();

pc.ontrack = (event) => {
    video.srcObject = event.streams[0];
};

// 连接到WebRTC网关
// 具体实现取决于WebRTC网关配置
```

## 性能监控

### 统计信息

服务器会定期输出统计信息：

```
=== RTSP Server Statistics ===
Uptime: 120.5 seconds
Total connections: 15
Active connections: 3
Frames served: 3615
Clients connected: 3
Error count: 0
Avg conversion time: 2.3 ms
```

### 性能指标

- **连接数**: 当前活跃的RTSP客户端连接数
- **帧服务数**: 已发送的视频帧总数
- **转换时间**: 视频格式转换的平均耗时
- **错误计数**: 发生的错误次数

### 监控脚本

```bash
# 实时监控服务器状态
watch -n 1 './scripts/start_rtsp_server.sh status'

# 查看实时日志
./scripts/start_rtsp_server.sh logs
```

## 故障排除

### 常见问题

1. **服务器启动失败**
   - 检查端口是否被占用: `netstat -an | grep :8554`
   - 检查GStreamer库是否安装: `pkg-config --exists gstreamer-rtsp-server-1.0`
   - 查看详细错误日志: `./scripts/start_rtsp_server.sh logs`

2. **客户端无法连接**
   - 检查防火墙设置
   - 验证RTSP URL格式: `rtsp://ip:port/mount`
   - 测试网络连通性: `telnet server_ip 8554`

3. **视频质量问题**
   - 调整码率: `--bitrate 5000000`
   - 检查硬件编码器支持: `gst-inspect-1.0 nvh264enc`
   - 监控CPU/GPU使用率

4. **延迟过高**
   - 减少缓冲区大小: `--buffer-size 2`
   - 使用硬件编码器: `--hw-encoder`
   - 减小GOP大小: `--gop-size 15`

### 调试模式

```bash
# 启用详细日志
export GST_DEBUG=3
./build/rtsp_server_main --topic VideoFrames

# 使用调试脚本
./scripts/start_rtsp_server.sh start --debug
```

## 集成指南

### 与其他服务集成

```bash
# 与视频捕获服务配合
./build/video_capture_main &
./build/rtsp_server_main --topic VideoFrames &

# 与AI处理服务配合
./build/ai_processor_main &
./build/rtsp_server_main --topic AIFrames --mount /ai &
```

### Docker部署

```dockerfile
FROM ubuntu:20.04

# 安装依赖
RUN apt-get update && apt-get install -y \
    libgstreamer1.0-dev \
    libgstreamer-plugins-base1.0-dev \
    libgstreamer-rtsp-server-1.0-dev \
    libfastdds-dev

# 复制程序
COPY build/rtsp_server_main /usr/local/bin/
COPY config/rtsp_server.json /etc/video_service/

# 暴露端口
EXPOSE 8554

# 启动服务
CMD ["rtsp_server_main", "--config", "/etc/video_service/rtsp_server.json"]
```

## API参考

### RTSPServerService类

```cpp
class RTSPServerService {
public:
    bool init(const RTSPServerConfig& config);
    bool start();
    void stop();
    bool is_running() const;
    
    ServerStats get_stats() const;
    void print_stats() const;
    
    bool update_bitrate(int new_bitrate);
    bool update_quality(int new_gop_size);
};
```

### 配置结构

```cpp
struct RTSPServerConfig {
    std::string dds_topic = "VideoFrames";
    std::string server_address = "0.0.0.0";
    int server_port = 8554;
    std::string mount_point = "/stream";
    
    int output_width = 1280;
    int output_height = 720;
    int output_fps = 30;
    int output_bitrate = 2000000;
    int gop_size = 30;
    
    bool use_hardware_encoder = true;
    bool zero_copy_mode = true;
    int max_clients = 10;
};
```

## 最佳实践

1. **性能优化**
   - 优先使用硬件编码器
   - 根据网络条件调整码率
   - 合理设置缓冲区大小

2. **稳定性**
   - 实现自动重连机制
   - 监控服务器状态
   - 设置合理的超时参数

3. **安全性**
   - 限制客户端连接数
   - 实现访问控制
   - 监控异常连接

4. **可维护性**
   - 使用配置文件管理参数
   - 实现详细的日志记录
   - 提供监控和诊断工具
