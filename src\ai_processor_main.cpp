#include "ai_processor.h"
#include "tensorrt_engine.h"
#include "onnx_engine.h"
#include <signal.h>
#include <getopt.h>
#include <iostream>

// 全局变量
std::unique_ptr<AIProcessor> g_ai_processor;

// 信号处理函数
void signal_handler(int signal) {
    switch (signal) {
        case SIGINT:
        case SIGTERM:
            LOG_I("Received termination signal, stopping...");
            if (g_ai_processor) {
                g_ai_processor->stop();
            }
            break;
        case SIGUSR1:
        case SIGUSR2:
            if (g_ai_processor) {
                g_ai_processor->handle_signal(signal);
            }
            break;
        default:
            break;
    }
}

void print_usage(const char* program_name) {
    std::cout << "Usage: " << program_name << " [OPTIONS]\n"
              << "Options:\n"
              << "  -e, --engine TYPE     AI engine type (tensorrt|onnx)\n"
              << "  -m, --model PATH      Model file path\n"
              << "  -c, --confidence VAL  Confidence threshold (0.0-1.0, default: 0.5)\n"
              << "  -v, --verbose         Enable verbose logging\n"
              << "  --help                Show this help message\n";
}

int main(int argc, char* argv[]) {
    // 设置日志级别
    Logger::set_level(LOG_INFO);
    
    // 默认配置
    AIConfig config;
    config.engine_type = "onnx";
    config.model_path = "model.onnx";
    config.confidence_threshold = 0.5f;
    
    // 解析命令行参数
    static struct option long_options[] = {
        {"engine", required_argument, 0, 'e'},
        {"model", required_argument, 0, 'm'},
        {"confidence", required_argument, 0, 'c'},
        {"verbose", no_argument, 0, 'v'},
        {"help", no_argument, 0, 1},
        {0, 0, 0, 0}
    };
    
    int c;
    while ((c = getopt_long(argc, argv, "e:m:c:v", long_options, nullptr)) != -1) {
        switch (c) {
            case 'e':
                config.engine_type = optarg;
                break;
            case 'm':
                config.model_path = optarg;
                break;
            case 'c':
                config.confidence_threshold = atof(optarg);
                if (config.confidence_threshold < 0.0f || config.confidence_threshold > 1.0f) {
                    std::cerr << "Confidence threshold must be between 0.0 and 1.0" << std::endl;
                    return 1;
                }
                break;
            case 'v':
                Logger::set_level(LOG_DEBUG);
                break;
            case 1:
                print_usage(argv[0]);
                return 0;
            default:
                print_usage(argv[0]);
                return 1;
        }
    }
    
    // 验证配置
    if (config.engine_type != "tensorrt" && config.engine_type != "onnx") {
        std::cerr << "Invalid engine type: " << config.engine_type << std::endl;
        return 1;
    }
    
    // 设置信号处理
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);
    signal(SIGUSR1, signal_handler);
    signal(SIGUSR2, signal_handler);
    
    LOG_I("Starting AI processor with %s engine...", config.engine_type.c_str());
    LOG_I("Model: %s, Confidence: %.2f",
          config.model_path.c_str(), config.confidence_threshold);
    
    try {
        // 创建AI处理器
        g_ai_processor = std::make_unique<AIProcessor>();

        // 创建AI引擎
        std::unique_ptr<AIEngine> ai_engine;
        if (config.engine_type == "tensorrt") {
            ai_engine = std::make_unique<TensorRTEngine>();
        } else if (config.engine_type == "onnx") {
            ai_engine = std::make_unique<ONNXEngine>();
        } else {
            LOG_E("Unsupported AI engine type: %s", config.engine_type.c_str());
            return false;
        }

        // 初始化AI处理器
        if (!g_ai_processor->init(ai_engine)) {
            LOG_E("Failed to initialize AI processor");
            return 1;
        }

        // 初始化AI引擎
        if (!ai_engine->init(config, [](const AIResult& result) {
            LOG_I("AI Result: %d, %f ms", result.valid, result.inference_time_ms);
        }));
        
        // 启动AI处理器
        g_ai_processor->start();
        
        // 主循环 - 定期输出AI处理器统计信息
        while (true) {
            std::this_thread::sleep_for(std::chrono::seconds(20));
            
            auto stats = g_ai_processor->get_stats();
            LOG_I("Stats - Processed: %lu, Dropped: %lu, FPS: %.1f, "
                  "Avg inference: %.1f ms, CPU: %.1f%%",
                  stats.frames_processed, stats.frames_dropped, stats.fps,
                  stats.avg_inference_time_ms, stats.cpu_usage);
        }
        
    } catch (const std::exception& e) {
        LOG_E("Exception in main: %s", e.what());
        return 1;
    }
    
    LOG_I("AI processor stopped");
    return 0;
}
