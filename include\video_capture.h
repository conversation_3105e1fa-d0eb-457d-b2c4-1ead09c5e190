
#ifndef VIDEO_CAPTURE_H
#define VIDEO_CAPTURE_H

#include "common.h"
#include "dds_interface.h"
#include <condition_variable>
#include <atomic>
#include <queue>
#include <thread>

// V4L2相关结构
struct V4L2Buffer {
    void* start;
    size_t length;
    int dma_fd;
};

// GStreamer RTSP客户端封装，不需要解码，只需编码包
class RTSPClient {
private:
    GstElement* pipeline_;
    GstElement* source_;
    GstElement* depay_;
    GstElement* converter_;
    GstElement* appsink_;
    GstBus* bus_;

    CaptureConfig config_;
    std::string url_;
    bool use_tcp_;
    bool connected_;

    // 帧缓冲区
    std::mutex frame_mutex_;
    std::queue<Frame> frame_queue_;
    std::condition_variable frame_cv_;

    // GStreamer回调
    static GstFlowReturn new_sample_callback(GstAppSink* appsink, gpointer user_data);
    static gboolean bus_callback(GstBus* bus, GstMessage* message, gpointer user_data);

    // 内部方法
    bool create_pipeline();
    void process_sample(GstSample* sample);
    void handle_bus_message(GstMessage* message);

public:
    RTSPClient() : pipeline_(nullptr), source_(nullptr), depay_(nullptr),
                   converter_(nullptr), appsink_(nullptr),
                   bus_(nullptr), use_tcp_(false), connected_(false) {}

    ~RTSPClient() {
        cleanup();
    }

    bool init(const std::string& url, bool use_tcp = false);
    Frame get_frame();
    void cleanup();
    bool is_connected() const { return connected_; }
};

class VideoCaptureService {

    typedef struct {
        int width;
        int height;
        int denominator;
        int numerator;
        int pixelformat;    // V4L2_PIX_FMT_XXX
    } VideoFormat;

private:
    CaptureConfig config_;
    std::atomic<bool> stop_requested_{false};
    std::atomic<bool> running_{false};
    std::thread capture_thread_;

    // V4L2相关
    int v4l2_fd_ = -1;
    std::vector<V4L2Buffer> v4l2_buffers_;
    v4l2_format v4l2_fmt_;
    bool use_dma_ = false;

    // RTSP相关
    std::unique_ptr<RTSPClient> rtsp_client_;

    // DDS
    std::unique_ptr<DDSWriter> dds_writer_;

    // 统计信息
    std::atomic<uint64_t> frame_id_counter_{0};
    std::atomic<uint64_t> frames_captured_{0};
    std::atomic<uint64_t> frames_dropped_{0};

    // 性能监控
    CPUMonitor cpu_monitor_;
    std::chrono::steady_clock::time_point last_adjust_time_;
    int current_frame_interval_ms_ = 33;  // 30fps

    // 查找最合适的格式
    VideoFormat findTheBestFormat(int fd, CaptureConfig& config) {
        VideoFormat bestFormat = {0, 0, 0, 0, 0};
        
        struct v4l2_fmtdesc fmtdesc;
        memset(&fmtdesc, 0, sizeof(fmtdesc));
        fmtdesc.type = V4L2_BUF_TYPE_VIDEO_CAPTURE;
        while (ioctl(fd, VIDIOC_ENUM_FMT, &fmtdesc) == 0) {
            if (fmtdesc.pixelformat != V4L2_PIX_FMT_YUYV &&
                fmtdesc.pixelformat != V4L2_PIX_FMT_NV12 &&
                fmtdesc.pixelformat != V4L2_PIX_FMT_RGB24 &&
                fmtdesc.pixelformat != V4L2_PIX_FMT_BGR24) { 
                continue;
            }
            struct v4l2_frmsizeenum frmsizeenum;
            memset(&frmsizeenum, 0, sizeof(frmsizeenum));
            frmsizeenum.pixel_format = fmtdesc.pixelformat;
            while (ioctl(fd, VIDIOC_ENUM_FRAMESIZES, &frmsizeenum) == 0) {
                if (frmsizeenum.type != V4L2_FRMSIZE_TYPE_DISCRETE) {
                    continue;
                }
                const uint32_t fmt_size = frmsizeenum.discrete.width * frmsizeenum.discrete.height;
                const uint32_t config_size = config.width * config.height;
                if (fmt_size / config_size > 2 || fmt_size / config_size < 0.5) {
                    continue;
                }
                struct v4l2_frmivalenum frmivalenum;
                memset(&frmivalenum, 0, sizeof(frmivalenum));
                frmivalenum.pixel_format = fmtdesc.pixelformat;
                frmivalenum.width = frmsizeenum.discrete.width;
                frmivalenum.height = frmsizeenum.discrete.height;
                while (ioctl(fd, VIDIOC_ENUM_FRAMEINTERVALS, &frmivalenum) == 0) {
                    if (frmivalenum.type == V4L2_FRMIVAL_TYPE_DISCRETE) {
                        int fps = frmivalenum.discrete.denominator / frmivalenum.discrete.numerator;
                        if (abs(fps - config.fps) < 10) {
                            bestFormat.width = frmsizeenum.discrete.width;
                            bestFormat.height = frmsizeenum.discrete.height;
                            bestFormat.denominator = frmivalenum.discrete.denominator;
                            bestFormat.numerator = frmivalenum.discrete.numerator;
                            bestFormat.pixelformat = fmtdesc.pixelformat;
                        }
                    }
                    frmivalenum.index++;
                }
                frmsizeenum.index++;
            }
            fmtdesc.index++;
        }
        return bestFormat;
    }

public:
    VideoCaptureService() = default;
    ~VideoCaptureService() {
        stop();
    }

    bool init(const CaptureConfig& config) {
        config_ = config;

        // 初始化DDS Writer
        dds_writer_ = std::make_unique<DDSWriter>();
        if (!dds_writer_->init("VideoFrames")) {
            LOG_E("Failed to initialize DDS writer");
            return false;
        }

        if (config.source_type == V4L2_SOURCE) {
            return init_v4l2();
        } else if (config.source_type == V4L2_SOURCE) {
            return init_rtsp();
        }

        LOG_E("Unknown video source type");
        return false;
    }


    void start() {
        if (running_.load()) {
            LOG_W("Video capture already running");
            return;
        }

        stop_requested_.store(false);
        capture_thread_ = std::thread(&VideoCaptureService::run, this);
        running_.store(true);
        LOG_I("Video capture started");
    }

    void stop() {
        if (!running_.load()) {
            return;
        }

        stop_requested_.store(true);
        if (capture_thread_.joinable()) {
            capture_thread_.join();
        }
        running_.store(false);

        cleanup();
        LOG_I("Video capture stopped");
    }

    void run() {
        LOG_I("Video capture thread started");
        last_adjust_time_ = std::chrono::steady_clock::now();

        while (!stop_requested_.load()) {
            try {
                auto frame = capture_frame();
                if (frame.valid) {
                    if (dds_writer_->write(frame)) {
                        frames_captured_.fetch_add(1);
                    } else {
                        frames_dropped_.fetch_add(1);
                        LOG_W("Failed to publish frame %lu", frame.frame_id);
                    }
                } else {
                    frames_dropped_.fetch_add(1);
                }

                // 动态帧率控制
                adjust_capture_rate();

            } catch (const std::exception& e) {
                LOG_E("Capture loop exception: %s", e.what());
                std::this_thread::sleep_for(std::chrono::milliseconds(100));
            }
        }

        LOG_I("Video capture thread stopped");
    }


    // 获取统计信息
    struct Stats {
        uint64_t frames_captured;
        uint64_t frames_dropped;
        float fps;
        float cpu_usage;
    };

    Stats get_stats() {
        static auto last_time = std::chrono::steady_clock::now();
        static uint64_t last_frames = 0;

        auto now = std::chrono::steady_clock::now();
        auto elapsed = std::chrono::duration_cast<std::chrono::seconds>(now - last_time).count();

        uint64_t current_frames = frames_captured_.load();
        float fps = 0.0f;
        if (elapsed > 0) {
            fps = (float)(current_frames - last_frames) / elapsed;
        }

        last_time = now;
        last_frames = current_frames;

        return {
            .frames_captured = current_frames,
            .frames_dropped = frames_dropped_.load(),
            .fps = fps,
            .cpu_usage = cpu_monitor_.get_usage()
        };
    }

private:
    bool init_v4l2() {
        // 打开设备
        v4l2_fd_ = open(config_.device.c_str(), O_RDWR | O_NONBLOCK);
        if (v4l2_fd_ < 0) {
            LOG_E("Failed to open V4L2 device %s: %s",
                  config_.device.c_str(), strerror(errno));
            return false;
        }

        // 查询设备能力
        v4l2_capability cap;
        if (ioctl(v4l2_fd_, VIDIOC_QUERYCAP, &cap) < 0) {
            LOG_E("Failed to query V4L2 capabilities: %s", strerror(errno));
            return false;
        }

        if (!(cap.capabilities & V4L2_CAP_VIDEO_CAPTURE)) {
            LOG_E("Device does not support video capture");
            return false;
        }

        VideoFormat bestFormat = findTheBestFormat(v4l2_fd_, config_);
        if (bestFormat.width == 0) {
            LOG_E("Failed to find suitable format");
            return false;
        }
        config_.width = bestFormat.width;
        config_.height = bestFormat.height;
        config_.fps = bestFormat.denominator / bestFormat.numerator;
        config_.format = bestFormat.pixelformat;
        LOG_D("Best format: %dx%d, %d/%d, %d", bestFormat.width, bestFormat.height,
              bestFormat.denominator, bestFormat.numerator, bestFormat.pixelformat);

        // 设置格式
        memset(&v4l2_fmt_, 0, sizeof(v4l2_fmt_));
        v4l2_fmt_.type = V4L2_BUF_TYPE_VIDEO_CAPTURE;
        v4l2_fmt_.fmt.pix.width = bestFormat.width;
        v4l2_fmt_.fmt.pix.height = bestFormat.height;
        v4l2_fmt_.fmt.pix.pixelformat = bestFormat.pixelformat;
        v4l2_fmt_.fmt.pix.field = V4L2_FIELD_INTERLACED;

        if (ioctl(v4l2_fd_, VIDIOC_S_FMT, &v4l2_fmt_) < 0) {
            LOG_E("Failed to set V4L2 format: %s", strerror(errno));
            return false;
        }

        // 设置帧率
        v4l2_streamparm parm;
        memset(&parm, 0, sizeof(parm));
        parm.type = V4L2_BUF_TYPE_VIDEO_CAPTURE;
        parm.parm.capture.timeperframe.numerator = bestFormat.numerator;
        parm.parm.capture.timeperframe.denominator = bestFormat.denominator;

        if (ioctl(v4l2_fd_, VIDIOC_S_PARM, &parm) < 0) {
            LOG_W("Failed to set framerate, using default");
        }

        // 初始化缓冲区
        return init_v4l2_buffers();
    }

    bool init_v4l2_buffers() {
        // 请求缓冲区
        v4l2_requestbuffers req;
        memset(&req, 0, sizeof(req));
        req.count = config_.buffer_count;
        req.type = V4L2_BUF_TYPE_VIDEO_CAPTURE;

        // 优先尝试DMA缓冲区
        if (config_.use_dma) {
            req.memory = V4L2_MEMORY_DMABUF;
            if (ioctl(v4l2_fd_, VIDIOC_REQBUFS, &req) == 0) {
                use_dma_ = true;
                LOG_I("Using DMA buffers");
            } else {
                LOG_W("DMA buffers not supported, falling back to MMAP");
            }
        }

        // 回退到MMAP
        if (!use_dma_) {
            req.memory = V4L2_MEMORY_MMAP;
            if (ioctl(v4l2_fd_, VIDIOC_REQBUFS, &req) < 0) {
                LOG_E("Failed to request V4L2 buffers: %s", strerror(errno));
                return false;
            }
        }

        // 映射缓冲区
        v4l2_buffers_.resize(req.count);
        for (uint32_t i = 0; i < req.count; ++i) {
            v4l2_buffer buf;
            memset(&buf, 0, sizeof(buf));
            buf.type = V4L2_BUF_TYPE_VIDEO_CAPTURE;
            buf.memory = req.memory;
            buf.index = i;

            if (ioctl(v4l2_fd_, VIDIOC_QUERYBUF, &buf) < 0) {
                LOG_E("Failed to query buffer %d: %s", i, strerror(errno));
                return false;
            }

            if (use_dma_) {
                v4l2_buffers_[i].dma_fd = buf.m.fd;
                v4l2_buffers_[i].length = buf.length;
                v4l2_buffers_[i].start = nullptr;
            } else {
                v4l2_buffers_[i].start = mmap(nullptr, buf.length,
                                            PROT_READ | PROT_WRITE, MAP_SHARED,
                                            v4l2_fd_, buf.m.offset);
                if (v4l2_buffers_[i].start == MAP_FAILED) {
                    LOG_E("Failed to mmap buffer %d: %s", i, strerror(errno));
                    return false;
                }
                v4l2_buffers_[i].length = buf.length;
                v4l2_buffers_[i].dma_fd = -1;
            }

            // 将缓冲区加入队列
            if (ioctl(v4l2_fd_, VIDIOC_QBUF, &buf) < 0) {
                LOG_E("Failed to queue buffer %d: %s", i, strerror(errno));
                return false;
            }
        }

        // 开始捕获
        enum v4l2_buf_type type = V4L2_BUF_TYPE_VIDEO_CAPTURE;
        if (ioctl(v4l2_fd_, VIDIOC_STREAMON, &type) < 0) {
            LOG_E("Failed to start V4L2 streaming: %s", strerror(errno));
            return false;
        }

        LOG_I("V4L2 initialized: %dx%d, %d buffers, DMA=%s",
              config_.width, config_.height, req.count, use_dma_ ? "yes" : "no");
        return true;
    }

    bool init_rtsp() {
        rtsp_client_ = std::make_unique<RTSPClient>();
        if (!rtsp_client_->init(config_.url, config_.use_tcp, config_.hw_decode)) {
            LOG_E("Failed to initialize RTSP client");
            return false;
        }

        LOG_I("RTSP client initialized: %s", config_.url.c_str());
        return true;
    }

    Frame capture_frame() {
        Frame frame;
        frame.frame_id = frame_id_counter_.fetch_add(1);
        frame.timestamp = get_current_us();

        if (config_.source_type == V4L2_SOURCE) {
            return capture_v4l2_frame();
        } else if (config_.source_type == RTSP_SOURCE) {
            return capture_rtsp_frame();
        }

        return frame;  // 无效帧
    }

    Frame capture_v4l2_frame() {
        Frame frame;

        // 等待帧就绪
        fd_set fds;
        FD_ZERO(&fds);
        FD_SET(v4l2_fd_, &fds);

        struct timeval tv;
        tv.tv_sec = 0;
        tv.tv_usec = 100000;  // 100ms超时

        int ret = select(v4l2_fd_ + 1, &fds, nullptr, nullptr, &tv);
        if (ret <= 0) {
            if (ret < 0 && errno != EINTR) {
                LOG_E("V4L2 select error: %s", strerror(errno));
            }
            return frame;  // 超时或错误
        }

        // 出队缓冲区
        v4l2_buffer buf;
        memset(&buf, 0, sizeof(buf));
        buf.type = V4L2_BUF_TYPE_VIDEO_CAPTURE;
        buf.memory = use_dma_ ? V4L2_MEMORY_DMABUF : V4L2_MEMORY_MMAP;

        if (ioctl(v4l2_fd_, VIDIOC_DQBUF, &buf) < 0) {
            if (errno != EAGAIN) {
                LOG_E("Failed to dequeue V4L2 buffer: %s", strerror(errno));
            }
            return frame;
        }

        // 填充帧信息
        frame.frame_id = frame_id_counter_.fetch_add(1);
        frame.timestamp = get_current_us();
        frame.width = v4l2_fmt_.fmt.pix.width;
        frame.height = v4l2_fmt_.fmt.pix.height;
        frame.format = config_.format;
        frame.source_type = V4L2_SOURCE;  // V4L2
        frame.is_keyframe = true;  // V4L2帧都是关键帧
        frame.valid = true;

        if (use_dma_) {
            frame.dma_fd = dup(v4l2_buffers_[buf.index].dma_fd);  // 复制文件描述符
            frame.data_length = buf.bytesused;
        } else {
            // 复制数据到软件缓冲区
            frame.data.resize(buf.bytesused);
            memcpy(frame.data.data(), v4l2_buffers_[buf.index].start, buf.bytesused);
        }

        // 重新入队缓冲区
        if (ioctl(v4l2_fd_, VIDIOC_QBUF, &buf) < 0) {
            LOG_E("Failed to requeue V4L2 buffer: %s", strerror(errno));
        }

        return frame;
    }

    Frame capture_rtsp_frame() {
        Frame frame;

        if (!rtsp_client_ || !rtsp_client_->is_connected()) {
            return frame;
        }

        try {
            frame = rtsp_client_->get_frame();
            if (frame.valid) {
                frame.frame_id = frame_id_counter_.fetch_add(1);
                frame.source_type = RTSP_SOURCE;  // RTSP
            }
        } catch (const std::exception& e) {
            LOG_E("RTSP capture exception: %s", e.what());
        }

        return frame;
    }
    
    void adjust_capture_rate() {
        auto now = std::chrono::steady_clock::now();
        auto elapsed = std::chrono::duration_cast<std::chrono::seconds>(
            now - last_adjust_time_).count();

        // 每5秒调整一次
        if (elapsed >= 5) {
            float cpu_usage = cpu_monitor_.get_usage();

            if (cpu_usage > 80.0f) {
                // CPU负载高，降低帧率
                current_frame_interval_ms_ = std::min(current_frame_interval_ms_ + 10, 100);
                LOG_I("High CPU usage (%.1f%%), reducing framerate to %d ms interval",
                      cpu_usage, current_frame_interval_ms_);
            } else if (cpu_usage < 50.0f && current_frame_interval_ms_ > 33) {
                // CPU负载低，提高帧率
                current_frame_interval_ms_ = std::max(current_frame_interval_ms_ - 10, 33);
                LOG_I("Low CPU usage (%.1f%%), increasing framerate to %d ms interval",
                      cpu_usage, current_frame_interval_ms_);
            }

            last_adjust_time_ = now;
        }

        // 控制帧率
        std::this_thread::sleep_for(std::chrono::milliseconds(current_frame_interval_ms_));
    }

    void cleanup() {
        // 停止V4L2流
        if (v4l2_fd_ >= 0) {
            enum v4l2_buf_type type = V4L2_BUF_TYPE_VIDEO_CAPTURE;
            ioctl(v4l2_fd_, VIDIOC_STREAMOFF, &type);

            // 释放缓冲区
            for (auto& buffer : v4l2_buffers_) {
                if (buffer.start && buffer.start != MAP_FAILED) {
                    munmap(buffer.start, buffer.length);
                }
                if (buffer.dma_fd >= 0) {
                    close(buffer.dma_fd);
                }
            }
            v4l2_buffers_.clear();

            close(v4l2_fd_);
            v4l2_fd_ = -1;
        }

        // 清理RTSP客户端
        rtsp_client_.reset();

        // 清理DDS
        dds_writer_.reset();

        LOG_I("Video capture cleanup completed");
    }

    // 信号处理函数
    void handle_signal(int signal) {
        switch (signal) {
            case SIGUSR1:
                // 降低分辨率/帧率
                current_frame_interval_ms_ = std::min(current_frame_interval_ms_ + 20, 100);
                LOG_I("Signal SIGUSR1: reduced framerate to %d ms", current_frame_interval_ms_);
                break;
            case SIGUSR2:
                // 恢复分辨率/帧率
                current_frame_interval_ms_ = std::max(current_frame_interval_ms_ - 20, 33);
                LOG_I("Signal SIGUSR2: increased framerate to %d ms", current_frame_interval_ms_);
                break;
            default:
                break;
        }
    }
};

// RTSPClient GStreamer实现
bool RTSPClient::init(const std::string& url, bool use_tcp) {
    url_ = url;
    use_tcp_ = use_tcp;

    // 初始化GStreamer
    if (!gst_is_initialized()) {
        gst_init(nullptr, nullptr);
    }

    // 创建管道
    if (!create_pipeline()) {
        LOG_E("Failed to create GStreamer pipeline");
        cleanup();
        return false;
    }

    // 设置管道状态为播放
    GstStateChangeReturn ret = gst_element_set_state(pipeline_, GST_STATE_PLAYING);
    if (ret == GST_STATE_CHANGE_FAILURE) {
        LOG_E("Failed to set pipeline to playing state");
        cleanup();
        return false;
    }

    // 等待管道准备就绪
    GstState state;
    ret = gst_element_get_state(pipeline_, &state, nullptr, GST_CLOCK_TIME_NONE);
    if (ret == GST_STATE_CHANGE_FAILURE || state != GST_STATE_PLAYING) {
        LOG_E("Pipeline failed to reach playing state");
        cleanup();
        return false;
    }

    connected_ = true;
    LOG_I("RTSP client initialized: %s, TCP=%s",
          url.c_str(), use_tcp ? "yes" : "no");

    return true;
}

bool RTSPClient::create_pipeline() {
    // 创建管道
    pipeline_ = gst_pipeline_new("rtsp-pipeline");
    if (!pipeline_) {
        LOG_E("Failed to create pipeline");
        return false;
    }

    // 创建RTSP源
    source_ = gst_element_factory_make("rtspsrc", "rtsp-source");
    if (!source_) {
        LOG_E("Failed to create rtspsrc element");
        return false;
    }

    // 设置RTSP源属性
    g_object_set(G_OBJECT(source_), "location", url_.c_str(), nullptr);
    g_object_set(G_OBJECT(source_), "latency", 0, nullptr);  // 0ms延迟
    g_object_set(G_OBJECT(source_), "buffer-mode", 1, nullptr);  // 低延迟模式
    g_object_set(G_OBJECT(source_), "drop-on-latency", TRUE, nullptr);

    if (use_tcp_) {
        g_object_set(G_OBJECT(source_), "protocols", 0x04, nullptr);  // TCP only
    }

    // 创建RTP depayloader (动态创建，因为不知道具体的编码格式)
    // 这将在pad-added信号中处理

    // 创建颜色空间转换器
    converter_ = gst_element_factory_make("videoconvert", "converter");
    if (!converter_) {
        LOG_E("Failed to create videoconvert element");
        return false;
    }

    // 创建appsink
    appsink_ = gst_element_factory_make("appsink", "app-sink");
    if (!appsink_) {
        LOG_E("Failed to create appsink element");
        return false;
    }

    // 设置appsink属性
    g_object_set(G_OBJECT(appsink_), "emit-signals", TRUE, nullptr);
    g_object_set(G_OBJECT(appsink_), "max-buffers", 1, nullptr);
    g_object_set(G_OBJECT(appsink_), "drop", TRUE, nullptr);

    // 设置caps (输出格式为H264)
    GstCaps* caps = gst_caps_new_simple("video/x-raw",
                                       "format", G_TYPE_STRING, "H264",
                                       nullptr);
    g_object_set(G_OBJECT(appsink_), "caps", caps, nullptr);
    gst_caps_unref(caps);

    // 连接new-sample信号
    g_signal_connect(appsink_, "new-sample", G_CALLBACK(new_sample_callback), this);

    // 添加元素到管道
    gst_bin_add_many(GST_BIN(pipeline_), source_, converter_, appsink_, nullptr);

    // 连接pad-added信号用于动态链接
    g_signal_connect(source_, "pad-added", G_CALLBACK([](GstElement* src, GstPad* new_pad, gpointer user_data) {
        RTSPClient* client = static_cast<RTSPClient*>(user_data);

        // 获取pad的caps
        GstCaps* caps = gst_pad_get_current_caps(new_pad);
        if (!caps) {
            caps = gst_pad_query_caps(new_pad, nullptr);
        }

        if (caps) {
            GstStructure* structure = gst_caps_get_structure(caps, 0);
            const gchar* name = gst_structure_get_name(structure);

            LOG_D("New pad: %s", name);

            // 检查是否是视频流
            if (g_str_has_prefix(name, "application/x-rtp")) {
                // 创建对应的depayloader
                GstElement* depay = nullptr;

                if (g_strrstr(name, "H264")) {
                    depay = gst_element_factory_make("rtph264depay", "h264-depay");
                    config_.format = V4L2_PIX_FMT_H264;
                } else if (g_strrstr(name, "H265")) {
                    depay = gst_element_factory_make("rtph265depay", "h265-depay");
                    config_.format = V4L2_PIX_FMT_H265;
                } else {
                    LOG_W("Unsupported RTP payload: %s", name);
                    gst_caps_unref(caps);
                    return;
                }

                if (depay) {
                    client->depay_ = depay;
                    gst_bin_add(GST_BIN(client->pipeline_), depay);

                    // 链接 source -> depay -> converter -> appsink
                    GstPad* sink_pad = gst_element_get_static_pad(depay, "sink");
                    if (gst_pad_link(new_pad, sink_pad) != GST_PAD_LINK_OK) {
                        LOG_E("Failed to link source to depayloader");
                    } else {
                        // 链接其余元素
                        if (!gst_element_link_many(depay, client->converter_, client->appsink_, nullptr)) {
                            LOG_E("Failed to link pipeline elements");
                        } else {
                            // 同步状态
                            gst_element_sync_state_with_parent(depay);
                        }
                    }
                    gst_object_unref(sink_pad);
                }
            }
            gst_caps_unref(caps);
        }
    }), this);

    // 设置总线监听
    bus_ = gst_element_get_bus(pipeline_);
    gst_bus_add_watch(bus_, bus_callback, this);

    return true;
}

Frame RTSPClient::get_frame() {
    Frame frame;

    if (!connected_) {
        return frame;
    }

    // 等待新帧
    std::unique_lock<std::mutex> lock(frame_mutex_);
    if (frame_cv_.wait_for(lock, std::chrono::milliseconds(100), [this] { return !frame_queue_.empty(); })) {
        frame = frame_queue_.front();
        frame_queue_.pop();
    }

    return frame;
}

// GStreamer回调函数实现
GstFlowReturn RTSPClient::new_sample_callback(GstAppSink* appsink, gpointer user_data) {
    RTSPClient* client = static_cast<RTSPClient*>(user_data);

    GstSample* sample = gst_app_sink_pull_sample(appsink);
    if (sample) {
        client->process_sample(sample);
        gst_sample_unref(sample);
    }

    return GST_FLOW_OK;
}

gboolean RTSPClient::bus_callback(GstBus* bus, GstMessage* message, gpointer user_data) {
    RTSPClient* client = static_cast<RTSPClient*>(user_data);
    client->handle_bus_message(message);
    return TRUE;
}

void RTSPClient::process_sample(GstSample* sample) {
    GstBuffer* buffer = gst_sample_get_buffer(sample);
    GstCaps* caps = gst_sample_get_caps(sample);

    if (!buffer || !caps) {
        return;
    }

    // 获取视频信息
    GstVideoInfo video_info;
    if (!gst_video_info_from_caps(&video_info, caps)) {
        LOG_E("Failed to get video info from caps");
        return;
    }

    // 创建Frame对象
    Frame frame;
    frame.timestamp = get_current_us();
    frame.width = GST_VIDEO_INFO_WIDTH(&video_info);
    frame.height = GST_VIDEO_INFO_HEIGHT(&video_info);
    frame.format = config_.format;  // GStreamer输出编码包格式
    frame.is_keyframe = !GST_BUFFER_FLAG_IS_SET(buffer, GST_BUFFER_FLAG_DELTA_UNIT);
    frame.valid = true;

    // 映射缓冲区数据
    GstMapInfo map_info;
    if (gst_buffer_map(buffer, &map_info, GST_MAP_READ)) {
        // 复制数据
        frame.data.resize(map_info.size);
        memcpy(frame.data.data(), map_info.data, map_info.size);
        gst_buffer_unmap(buffer, &map_info);

        // 添加到帧队列
        std::lock_guard<std::mutex> lock(frame_mutex_);

        // 保持队列大小，丢弃旧帧
        while (frame_queue_.size() >= 5) {
            frame_queue_.pop();
        }

        frame_queue_.push(frame);
        frame_cv_.notify_one();
    } else {
        LOG_E("Failed to map GStreamer buffer");
    }
}

void RTSPClient::handle_bus_message(GstMessage* message) {
    switch (GST_MESSAGE_TYPE(message)) {
        case GST_MESSAGE_ERROR: {
            GError* error;
            gchar* debug;
            gst_message_parse_error(message, &error, &debug);
            LOG_E("GStreamer error: %s", error->message);
            if (debug) {
                LOG_D("Debug info: %s", debug);
                g_free(debug);
            }
            g_error_free(error);
            connected_ = false;
            break;
        }
        case GST_MESSAGE_WARNING: {
            GError* error;
            gchar* debug;
            gst_message_parse_warning(message, &error, &debug);
            LOG_W("GStreamer warning: %s", error->message);
            if (debug) {
                LOG_D("Debug info: %s", debug);
                g_free(debug);
            }
            g_error_free(error);
            break;
        }
        case GST_MESSAGE_EOS:
            LOG_I("RTSP stream ended");
            connected_ = false;
            break;
        case GST_MESSAGE_STATE_CHANGED: {
            if (GST_MESSAGE_SRC(message) == GST_OBJECT(pipeline_)) {
                GstState old_state, new_state;
                gst_message_parse_state_changed(message, &old_state, &new_state, nullptr);
                LOG_D("Pipeline state changed from %s to %s",
                      gst_element_state_get_name(old_state),
                      gst_element_state_get_name(new_state));
            }
            break;
        }
        default:
            break;
    }
}

void RTSPClient::cleanup() {
    connected_ = false;

    // 停止管道
    if (pipeline_) {
        gst_element_set_state(pipeline_, GST_STATE_NULL);

        // 等待状态改变完成
        GstState state;
        gst_element_get_state(pipeline_, &state, nullptr, GST_CLOCK_TIME_NONE);

        gst_object_unref(pipeline_);
        pipeline_ = nullptr;
    }

    // 清理总线
    if (bus_) {
        gst_object_unref(bus_);
        bus_ = nullptr;
    }

    // 重置元素指针
    source_ = nullptr;
    depay_ = nullptr;
    converter_ = nullptr;
    appsink_ = nullptr;

    // 清空帧队列
    std::lock_guard<std::mutex> lock(frame_mutex_);
    while (!frame_queue_.empty()) {
        frame_queue_.pop();
    }

    LOG_I("RTSP client cleanup completed");
}

#endif // VIDEO_CAPTURE_H

