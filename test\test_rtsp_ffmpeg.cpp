/**
 * @file test_rtsp_ffmpeg.cpp
 * @brief FFmpeg RTSP客户端测试程序 (仅获取编码包)
 */

#include <iostream>
#include <iomanip>
#include <chrono>
#include <thread>
#include <signal.h>
#include "../include/video_capture.h"

static bool g_running = true;

void signal_handler(int signal) {
    std::cout << "\nReceived signal " << signal << ", shutting down..." << std::endl;
    g_running = false;
}

void print_usage(const char* program_name) {
    std::cout << "Usage: " << program_name << " <rtsp_url> [tcp]" << std::endl;
    std::cout << "  rtsp_url  - RTSP stream URL (e.g., rtsp://*************:554/stream)" << std::endl;
    std::cout << "  tcp       - Use TCP transport (optional)" << std::endl;
    std::cout << std::endl;
    std::cout << "Examples:" << std::endl;
    std::cout << "  " << program_name << " rtsp://*************:554/stream" << std::endl;
    std::cout << "  " << program_name << " rtsp://*************:554/stream tcp" << std::endl;
}

int main(int argc, char* argv[]) {
    // 设置信号处理
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);

    if (argc < 2) {
        print_usage(argv[0]);
        return 1;
    }

    std::string rtsp_url = argv[1];
    bool use_tcp = false;

    // 解析命令行参数
    for (int i = 2; i < argc; i++) {
        std::string arg = argv[i];
        if (arg == "tcp") {
            use_tcp = true;
        }
    }

    std::cout << "=== FFmpeg RTSP Client Test ===" << std::endl;
    std::cout << "RTSP URL: " << rtsp_url << std::endl;
    std::cout << "Transport: " << (use_tcp ? "TCP" : "UDP") << std::endl;
    std::cout << "Mode: Encoded packets only (no decoding)" << std::endl;
    std::cout << std::endl;

    // 创建RTSP客户端
    RTSPClient client;

    std::cout << "Initializing RTSP client..." << std::endl;
    if (!client.init(rtsp_url, use_tcp)) {
        std::cerr << "Failed to initialize RTSP client" << std::endl;
        return 1;
    }

    std::cout << "RTSP client initialized successfully!" << std::endl;
    std::cout << "Stream info:" << std::endl;
    std::cout << "  Resolution: " << client.get_width() << "x" << client.get_height() << std::endl;
    
    if (client.get_codec_params()) {
        std::cout << "  Codec: " << avcodec_get_name(client.get_codec_params()->codec_id) << std::endl;
        std::cout << "  Bitrate: " << client.get_codec_params()->bit_rate << " bps" << std::endl;
    }
    
    std::cout << std::endl;

    // 统计信息
    int frame_count = 0;
    int keyframe_count = 0;
    size_t total_bytes = 0;
    auto start_time = std::chrono::steady_clock::now();
    auto last_stats_time = start_time;

    std::cout << "Starting packet capture... (Press Ctrl+C to stop)" << std::endl;
    std::cout << std::endl;

    while (g_running && client.is_connected()) {
        Frame frame = client.get_frame();
        
        if (frame.valid) {
            frame_count++;
            total_bytes += frame.data.size();
            
            if (frame.is_keyframe) {
                keyframe_count++;
            }

            // 每5秒显示一次统计信息
            auto current_time = std::chrono::steady_clock::now();
            auto elapsed = std::chrono::duration_cast<std::chrono::seconds>(current_time - last_stats_time);
            
            if (elapsed.count() >= 5) {
                auto total_elapsed = std::chrono::duration_cast<std::chrono::seconds>(current_time - start_time);
                double fps = static_cast<double>(frame_count) / total_elapsed.count();
                double keyframe_ratio = static_cast<double>(keyframe_count) / frame_count * 100.0;
                double avg_packet_size = static_cast<double>(total_bytes) / frame_count;
                double bitrate_mbps = static_cast<double>(total_bytes * 8) / (total_elapsed.count() * 1024 * 1024);

                std::cout << "\r" << std::string(80, ' ') << "\r"; // 清除行
                std::cout << "Packets: " << frame_count 
                         << ", FPS: " << std::fixed << std::setprecision(1) << fps
                         << ", Keyframes: " << std::setprecision(1) << keyframe_ratio << "%"
                         << ", Avg size: " << std::setprecision(0) << avg_packet_size << " bytes"
                         << ", Bitrate: " << std::setprecision(2) << bitrate_mbps << " Mbps"
                         << std::flush;

                last_stats_time = current_time;
            }
        } else {
            // 没有数据时短暂休眠
            std::this_thread::sleep_for(std::chrono::milliseconds(10));
        }
    }

    std::cout << std::endl << std::endl;

    // 最终统计
    auto end_time = std::chrono::steady_clock::now();
    auto total_elapsed = std::chrono::duration_cast<std::chrono::seconds>(end_time - start_time);
    
    if (total_elapsed.count() > 0) {
        double fps = static_cast<double>(frame_count) / total_elapsed.count();
        double keyframe_ratio = frame_count > 0 ? static_cast<double>(keyframe_count) / frame_count * 100.0 : 0.0;
        double avg_packet_size = frame_count > 0 ? static_cast<double>(total_bytes) / frame_count : 0.0;
        double bitrate_mbps = static_cast<double>(total_bytes * 8) / (total_elapsed.count() * 1024 * 1024);

        std::cout << "=== Final Statistics ===" << std::endl;
        std::cout << "Total runtime: " << total_elapsed.count() << " seconds" << std::endl;
        std::cout << "Total packets: " << frame_count << std::endl;
        std::cout << "Keyframe packets: " << keyframe_count << " (" << std::setprecision(1) << keyframe_ratio << "%)" << std::endl;
        std::cout << "Average FPS: " << std::setprecision(1) << fps << std::endl;
        std::cout << "Total data: " << std::setprecision(2) << static_cast<double>(total_bytes) / (1024 * 1024) << " MB" << std::endl;
        std::cout << "Average packet size: " << std::setprecision(0) << avg_packet_size << " bytes" << std::endl;
        std::cout << "Average bitrate: " << std::setprecision(2) << bitrate_mbps << " Mbps" << std::endl;
    }

    std::cout << "Cleaning up..." << std::endl;
    client.cleanup();
    
    std::cout << "Test completed successfully!" << std::endl;
    return 0;
}
