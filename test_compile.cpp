/**
 * @file test_compile.cpp
 * @brief 简单的编译测试程序
 */

#include <iostream>
#include <string>
#include <vector>
#include <memory>
#include <thread>
#include <mutex>
#include <condition_variable>
#include <queue>
#include <chrono>

// 测试是否可以包含我们的头文件
#include "include/common.h"

// 简单的测试函数
void test_frame_structure() {
    std::cout << "Testing Frame structure..." << std::endl;
    
    Frame frame;
    frame.valid = true;
    frame.width = 1920;
    frame.height = 1080;
    frame.format = "H264";
    frame.timestamp = std::chrono::duration_cast<std::chrono::milliseconds>(
        std::chrono::steady_clock::now().time_since_epoch()).count();
    frame.is_keyframe = true;
    
    // 添加一些测试数据
    std::string test_data = "Test NALU data";
    frame.data.assign(test_data.begin(), test_data.end());
    
    std::cout << "Frame created successfully:" << std::endl;
    std::cout << "  Size: " << frame.width << "x" << frame.height << std::endl;
    std::cout << "  Format: " << frame.format << std::endl;
    std::cout << "  Data size: " << frame.data.size() << " bytes" << std::endl;
    std::cout << "  Is keyframe: " << (frame.is_keyframe ? "Yes" : "No") << std::endl;
}

void test_config_structure() {
    std::cout << "\nTesting Config structure..." << std::endl;
    
    Config config;
    config.rtsp_url = "rtsp://192.168.1.100:554/stream";
    config.width = 1920;
    config.height = 1080;
    config.fps = 30;
    config.use_tcp = true;
    
    std::cout << "Config created successfully:" << std::endl;
    std::cout << "  RTSP URL: " << config.rtsp_url << std::endl;
    std::cout << "  Resolution: " << config.width << "x" << config.height << std::endl;
    std::cout << "  FPS: " << config.fps << std::endl;
    std::cout << "  Use TCP: " << (config.use_tcp ? "Yes" : "No") << std::endl;
}

int main() {
    std::cout << "=== Video Service Compilation Test ===" << std::endl;
    std::cout << "Testing basic structures and includes..." << std::endl;
    
    try {
        test_frame_structure();
        test_config_structure();
        
        std::cout << "\n=== All tests passed! ===" << std::endl;
        std::cout << "Headers compile successfully." << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "Test failed with exception: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}
