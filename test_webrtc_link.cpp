// 测试WebRTC库链接的简单程序
#include <gst/gst.h>
#include <gst/webrtc/webrtc.h>
#include <gst/sdp/sdp.h>
#include <iostream>

int main() {
    try {
        std::cout << "Testing GStreamer WebRTC library linking..." << std::endl;
        
        // 初始化GStreamer
        gst_init(nullptr, nullptr);
        std::cout << "GStreamer initialized successfully" << std::endl;
        
        // 测试创建WebRTC session description
        GstSDPMessage* sdp_msg = nullptr;
        gst_sdp_message_new(&sdp_msg);
        
        if (sdp_msg) {
            std::cout << "SDP message created successfully" << std::endl;
            
            // 创建WebRTC session description
            GstWebRTCSessionDescription* desc = gst_webrtc_session_description_new(
                GST_WEBRTC_SDP_TYPE_OFFER, sdp_msg);
            
            if (desc) {
                std::cout << "WebRTC session description created successfully" << std::endl;
                
                // 测试释放函数 - 这是导致链接错误的函数
                gst_webrtc_session_description_free(desc);
                std::cout << "WebRTC session description freed successfully" << std::endl;
            } else {
                std::cerr << "Failed to create WebRTC session description" << std::endl;
                gst_sdp_message_free(sdp_msg);
                return 1;
            }
        } else {
            std::cerr << "Failed to create SDP message" << std::endl;
            return 1;
        }
        
        // 清理GStreamer
        gst_deinit();
        std::cout << "All WebRTC tests passed!" << std::endl;
        return 0;
        
    } catch (const std::exception& e) {
        std::cerr << "WebRTC test failed: " << e.what() << std::endl;
        return 1;
    }
}
