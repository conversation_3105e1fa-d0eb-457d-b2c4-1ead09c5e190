
cmake_minimum_required(VERSION 3.20)

set(LIB_NAME "DDSVideoFrame_lib_${PROJECT_NAME}")  # 添加项目前缀

set(CMAKE_CXX_STANDARD 11)
set(CMAKE_CXX_EXTENSIONS OFF)

# Find requirements
find_package(fastcdr REQUIRED)
find_package(fastdds 3 REQUIRED)

message(STATUS "Configuring DDSVideoFrame...")
add_library(${LIB_NAME} SHARED DDSVideoFrameTypeObjectSupport.cxx DDSVideoFramePubSubTypes.cxx dds_video_reader.cxx dds_video_writer.cxx)
# 暴露头文件
target_include_directories(${LIB_NAME} PUBLIC 
    $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}>
    $<INSTALL_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}>
)
target_link_libraries(${LIB_NAME} fastcdr fastdds)

# add_executable(DDSVideoFrame DDSVideoFrameApplication.cxx DDSVideoFramePublisherApp.cxx DDSVideoFrameSubscriberApp.cxx DDSVideoFramemain.cxx)
# target_link_libraries(DDSVideoFrame fastcdr fastdds ${LIB_NAME})



