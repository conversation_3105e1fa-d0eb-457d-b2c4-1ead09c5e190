// Copyright 2016 Proyectos y Sistemas de Mantenimiento SL (eProsima).
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

/*!
 * @file DDSVideoFrame.hpp
 * This header file contains the declaration of the described types in the IDL file.
 *
 * This file was generated by the tool fastddsgen.
 */

#ifndef FAST_DDS_GENERATED__DDSVIDEOFRAME_HPP
#define FAST_DDS_GENERATED__DDSVIDEOFRAME_HPP

#include <cstdint>
#include <utility>
#include <vector>


#if defined(_WIN32)
#if defined(EPROSIMA_USER_DLL_EXPORT)
#define eProsima_user_DllExport __declspec( dllexport )
#else
#define eProsima_user_DllExport
#endif  // EPROSIMA_USER_DLL_EXPORT
#else
#define eProsima_user_DllExport
#endif  // _WIN32

#if defined(_WIN32)
#if defined(EPROSIMA_USER_DLL_EXPORT)
#if defined(DDSVIDEOFRAME_SOURCE)
#define DDSVIDEOFRAME_DllAPI __declspec( dllexport )
#else
#define DDSVIDEOFRAME_DllAPI __declspec( dllimport )
#endif // DDSVIDEOFRAME_SOURCE
#else
#define DDSVIDEOFRAME_DllAPI
#endif  // EPROSIMA_USER_DLL_EXPORT
#else
#define DDSVIDEOFRAME_DllAPI
#endif // _WIN32

/*!
 * @brief This class represents the structure DDSVideoFrame defined by the user in the IDL file.
 * @ingroup DDSVideoFrame
 */
class DDSVideoFrame
{
public:

    /*!
     * @brief Default constructor.
     */
    eProsima_user_DllExport DDSVideoFrame()
    {
    }

    /*!
     * @brief Default destructor.
     */
    eProsima_user_DllExport ~DDSVideoFrame()
    {
    }

    /*!
     * @brief Copy constructor.
     * @param x Reference to the object DDSVideoFrame that will be copied.
     */
    eProsima_user_DllExport DDSVideoFrame(
            const DDSVideoFrame& x)
    {
                    m_frame_id = x.m_frame_id;

                    m_timestamp = x.m_timestamp;

                    m_width = x.m_width;

                    m_height = x.m_height;

                    m_format = x.m_format;

                    m_dma_fd = x.m_dma_fd;

                    m_source_type = x.m_source_type;

                    m_data_length = x.m_data_length;

                    m_is_keyframe = x.m_is_keyframe;

                    m_offset = x.m_offset;

                    m_data = x.m_data;

    }

    /*!
     * @brief Move constructor.
     * @param x Reference to the object DDSVideoFrame that will be copied.
     */
    eProsima_user_DllExport DDSVideoFrame(
            DDSVideoFrame&& x) noexcept
    {
        m_frame_id = x.m_frame_id;
        m_timestamp = x.m_timestamp;
        m_width = x.m_width;
        m_height = x.m_height;
        m_format = x.m_format;
        m_dma_fd = x.m_dma_fd;
        m_source_type = x.m_source_type;
        m_data_length = x.m_data_length;
        m_is_keyframe = x.m_is_keyframe;
        m_offset = x.m_offset;
        m_data = std::move(x.m_data);
    }

    /*!
     * @brief Copy assignment.
     * @param x Reference to the object DDSVideoFrame that will be copied.
     */
    eProsima_user_DllExport DDSVideoFrame& operator =(
            const DDSVideoFrame& x)
    {

                    m_frame_id = x.m_frame_id;

                    m_timestamp = x.m_timestamp;

                    m_width = x.m_width;

                    m_height = x.m_height;

                    m_format = x.m_format;

                    m_dma_fd = x.m_dma_fd;

                    m_source_type = x.m_source_type;

                    m_data_length = x.m_data_length;

                    m_is_keyframe = x.m_is_keyframe;

                    m_offset = x.m_offset;

                    m_data = x.m_data;

        return *this;
    }

    /*!
     * @brief Move assignment.
     * @param x Reference to the object DDSVideoFrame that will be copied.
     */
    eProsima_user_DllExport DDSVideoFrame& operator =(
            DDSVideoFrame&& x) noexcept
    {

        m_frame_id = x.m_frame_id;
        m_timestamp = x.m_timestamp;
        m_width = x.m_width;
        m_height = x.m_height;
        m_format = x.m_format;
        m_dma_fd = x.m_dma_fd;
        m_source_type = x.m_source_type;
        m_data_length = x.m_data_length;
        m_is_keyframe = x.m_is_keyframe;
        m_offset = x.m_offset;
        m_data = std::move(x.m_data);
        return *this;
    }

    /*!
     * @brief Comparison operator.
     * @param x DDSVideoFrame object to compare.
     */
    eProsima_user_DllExport bool operator ==(
            const DDSVideoFrame& x) const
    {
        return (m_frame_id == x.m_frame_id &&
           m_timestamp == x.m_timestamp &&
           m_width == x.m_width &&
           m_height == x.m_height &&
           m_format == x.m_format &&
           m_dma_fd == x.m_dma_fd &&
           m_source_type == x.m_source_type &&
           m_data_length == x.m_data_length &&
           m_is_keyframe == x.m_is_keyframe &&
           m_offset == x.m_offset &&
           m_data == x.m_data);
    }

    /*!
     * @brief Comparison operator.
     * @param x DDSVideoFrame object to compare.
     */
    eProsima_user_DllExport bool operator !=(
            const DDSVideoFrame& x) const
    {
        return !(*this == x);
    }

    /*!
     * @brief This function sets a value in member frame_id
     * @param _frame_id New value for member frame_id
     */
    eProsima_user_DllExport void frame_id(
            uint64_t _frame_id)
    {
        m_frame_id = _frame_id;
    }

    /*!
     * @brief This function returns the value of member frame_id
     * @return Value of member frame_id
     */
    eProsima_user_DllExport uint64_t frame_id() const
    {
        return m_frame_id;
    }

    /*!
     * @brief This function returns a reference to member frame_id
     * @return Reference to member frame_id
     */
    eProsima_user_DllExport uint64_t& frame_id()
    {
        return m_frame_id;
    }


    /*!
     * @brief This function sets a value in member timestamp
     * @param _timestamp New value for member timestamp
     */
    eProsima_user_DllExport void timestamp(
            uint64_t _timestamp)
    {
        m_timestamp = _timestamp;
    }

    /*!
     * @brief This function returns the value of member timestamp
     * @return Value of member timestamp
     */
    eProsima_user_DllExport uint64_t timestamp() const
    {
        return m_timestamp;
    }

    /*!
     * @brief This function returns a reference to member timestamp
     * @return Reference to member timestamp
     */
    eProsima_user_DllExport uint64_t& timestamp()
    {
        return m_timestamp;
    }


    /*!
     * @brief This function sets a value in member width
     * @param _width New value for member width
     */
    eProsima_user_DllExport void width(
            uint16_t _width)
    {
        m_width = _width;
    }

    /*!
     * @brief This function returns the value of member width
     * @return Value of member width
     */
    eProsima_user_DllExport uint16_t width() const
    {
        return m_width;
    }

    /*!
     * @brief This function returns a reference to member width
     * @return Reference to member width
     */
    eProsima_user_DllExport uint16_t& width()
    {
        return m_width;
    }


    /*!
     * @brief This function sets a value in member height
     * @param _height New value for member height
     */
    eProsima_user_DllExport void height(
            uint16_t _height)
    {
        m_height = _height;
    }

    /*!
     * @brief This function returns the value of member height
     * @return Value of member height
     */
    eProsima_user_DllExport uint16_t height() const
    {
        return m_height;
    }

    /*!
     * @brief This function returns a reference to member height
     * @return Reference to member height
     */
    eProsima_user_DllExport uint16_t& height()
    {
        return m_height;
    }


    /*!
     * @brief This function sets a value in member format
     * @param _format New value for member format
     */
    eProsima_user_DllExport void format(
            int32_t _format)
    {
        m_format = _format;
    }

    /*!
     * @brief This function returns the value of member format
     * @return Value of member format
     */
    eProsima_user_DllExport int32_t format() const
    {
        return m_format;
    }

    /*!
     * @brief This function returns a reference to member format
     * @return Reference to member format
     */
    eProsima_user_DllExport int32_t& format()
    {
        return m_format;
    }


    /*!
     * @brief This function sets a value in member dma_fd
     * @param _dma_fd New value for member dma_fd
     */
    eProsima_user_DllExport void dma_fd(
            int32_t _dma_fd)
    {
        m_dma_fd = _dma_fd;
    }

    /*!
     * @brief This function returns the value of member dma_fd
     * @return Value of member dma_fd
     */
    eProsima_user_DllExport int32_t dma_fd() const
    {
        return m_dma_fd;
    }

    /*!
     * @brief This function returns a reference to member dma_fd
     * @return Reference to member dma_fd
     */
    eProsima_user_DllExport int32_t& dma_fd()
    {
        return m_dma_fd;
    }


    /*!
     * @brief This function sets a value in member source_type
     * @param _source_type New value for member source_type
     */
    eProsima_user_DllExport void source_type(
            uint8_t _source_type)
    {
        m_source_type = _source_type;
    }

    /*!
     * @brief This function returns the value of member source_type
     * @return Value of member source_type
     */
    eProsima_user_DllExport uint8_t source_type() const
    {
        return m_source_type;
    }

    /*!
     * @brief This function returns a reference to member source_type
     * @return Reference to member source_type
     */
    eProsima_user_DllExport uint8_t& source_type()
    {
        return m_source_type;
    }


    /*!
     * @brief This function sets a value in member data_length
     * @param _data_length New value for member data_length
     */
    eProsima_user_DllExport void data_length(
            uint32_t _data_length)
    {
        m_data_length = _data_length;
    }

    /*!
     * @brief This function returns the value of member data_length
     * @return Value of member data_length
     */
    eProsima_user_DllExport uint32_t data_length() const
    {
        return m_data_length;
    }

    /*!
     * @brief This function returns a reference to member data_length
     * @return Reference to member data_length
     */
    eProsima_user_DllExport uint32_t& data_length()
    {
        return m_data_length;
    }


    /*!
     * @brief This function sets a value in member is_keyframe
     * @param _is_keyframe New value for member is_keyframe
     */
    eProsima_user_DllExport void is_keyframe(
            bool _is_keyframe)
    {
        m_is_keyframe = _is_keyframe;
    }

    /*!
     * @brief This function returns the value of member is_keyframe
     * @return Value of member is_keyframe
     */
    eProsima_user_DllExport bool is_keyframe() const
    {
        return m_is_keyframe;
    }

    /*!
     * @brief This function returns a reference to member is_keyframe
     * @return Reference to member is_keyframe
     */
    eProsima_user_DllExport bool& is_keyframe()
    {
        return m_is_keyframe;
    }


    /*!
     * @brief This function sets a value in member offset
     * @param _offset New value for member offset
     */
    eProsima_user_DllExport void offset(
            uint32_t _offset)
    {
        m_offset = _offset;
    }

    /*!
     * @brief This function returns the value of member offset
     * @return Value of member offset
     */
    eProsima_user_DllExport uint32_t offset() const
    {
        return m_offset;
    }

    /*!
     * @brief This function returns a reference to member offset
     * @return Reference to member offset
     */
    eProsima_user_DllExport uint32_t& offset()
    {
        return m_offset;
    }


    /*!
     * @brief This function copies the value in member data
     * @param _data New value to be copied in member data
     */
    eProsima_user_DllExport void data(
            const std::vector<uint8_t>& _data)
    {
        m_data = _data;
    }

    /*!
     * @brief This function moves the value in member data
     * @param _data New value to be moved in member data
     */
    eProsima_user_DllExport void data(
            std::vector<uint8_t>&& _data)
    {
        m_data = std::move(_data);
    }

    /*!
     * @brief This function returns a constant reference to member data
     * @return Constant reference to member data
     */
    eProsima_user_DllExport const std::vector<uint8_t>& data() const
    {
        return m_data;
    }

    /*!
     * @brief This function returns a reference to member data
     * @return Reference to member data
     */
    eProsima_user_DllExport std::vector<uint8_t>& data()
    {
        return m_data;
    }



private:

    uint64_t m_frame_id{0};
    uint64_t m_timestamp{0};
    uint16_t m_width{0};
    uint16_t m_height{0};
    int32_t m_format{0};
    int32_t m_dma_fd{0};
    uint8_t m_source_type{0};
    uint32_t m_data_length{0};
    bool m_is_keyframe{false};
    uint32_t m_offset{0};
    std::vector<uint8_t> m_data;

};

#endif // _FAST_DDS_GENERATED_DDSVIDEOFRAME_HPP_


