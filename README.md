# Embedded Linux Video Service System

A high-performance, modular video processing system designed for embedded Linux environments. The system captures video from local V4L2 devices or remote RTSP cameras, processes frames through AI inference, and streams to cloud platforms via WebRTC or RTMP.

## Architecture

The system consists of four main components:

1. **Video Capture Service** - Captures video from V4L2 devices or RTSP streams
2. **Video Converter Service** - Converts and splits video streams for AI and cloud processing
3. **AI Processor Service** - Performs real-time AI inference on video frames
4. **Cloud Streamer Service** - Streams processed video to cloud platforms

All components communicate via DDS (Data Distribution Service) for low-latency, real-time data exchange.

## Features

- **Multi-source Support**: V4L2 cameras and RTSP streams
- **Hardware Acceleration**: DMA buffers, V4L2 M2M, VAAPI encoding
- **AI Integration**: TensorRT and ONNX Runtime support
- **Cloud Streaming**: WebRTC and RTMP protocols
- **Real-time Performance**: Zero-copy operations where possible
- **Resource Management**: CPU affinity, dynamic frame rate control
- **Monitoring**: Comprehensive logging and statistics

## Dependencies

### Required
- **FFmpeg** (>= 4.0) - Video processing and RTSP support
- **GStreamer** (>= 1.16) - Media pipeline and WebRTC/RTMP streaming
- **Fast DDS** (>= 2.0) - Inter-process communication
- **Linux Kernel** (>= 4.14) - V4L2 and DMA buffer support

### Optional
- **TensorRT** (>= 8.0) - NVIDIA GPU acceleration for AI inference
- **ONNX Runtime** (>= 1.8) - Cross-platform AI inference
- **OpenCV** (>= 4.0) - Additional image processing utilities

## Building

### Using CMake (Recommended)

```bash
mkdir build && cd build
cmake ..
make -j$(nproc)
sudo make install
```

### Using Makefile

```bash
make check-deps  # Check dependencies
make config      # Show configuration
make -j$(nproc)  # Build
sudo make install
```

### Build Options

- `BUILD_TYPE=Debug|Release` - Build configuration
- `PREFIX=/path` - Installation prefix
- `TENSORRT_ROOT=/path` - TensorRT installation path
- `ONNXRUNTIME_ROOT=/path` - ONNX Runtime installation path

## Configuration

Edit `config.json` to configure the system:

```json
{
  "video_capture": {
    "source_type": "v4l2",
    "device": "/dev/video0",
    "width": 1280,
    "height": 720,
    "fps": 30
  },
  "ai_processor": {
    "engine_type": "onnx",
    "model_path": "/opt/models/yolov5s.onnx",
    "confidence_threshold": 0.5
  },
  "cloud_streamer": {
    "type": "rtmp",
    "rtmp_url": "rtmp://live.example.com/live/stream_key"
  }
}
```

## Usage

### Manual Start

Start individual services:

```bash
# Start video capture
./video_capture_main --source v4l2 --device /dev/video0

# Start video converter
./video_converter_main --verbose

# Start AI processor
./ai_processor_main --engine onnx --model model.onnx

# Start cloud streamer
./cloud_streamer_main --type rtmp --url rtmp://example.com/live/key
```

### Service Manager

Use the service manager script:

```bash
# Start all services
sudo ./video_service_manager.sh start

# Check status
./video_service_manager.sh status

# View logs
./video_service_manager.sh logs video_capture

# Stop all services
sudo ./video_service_manager.sh stop
```

### Systemd Service

Install and enable the systemd service:

```bash
sudo systemctl enable video_service
sudo systemctl start video_service
sudo systemctl status video_service
```

## Performance Tuning

### System Configuration

1. **CPU Isolation**: Reserve CPU cores for video processing
   ```bash
   # Add to kernel command line
   isolcpus=4-7 nohz_full=4-7 rcu_nocbs=4-7
   ```

2. **Memory Configuration**: Increase DMA buffer sizes
   ```bash
   echo 'vm.nr_hugepages=256' >> /etc/sysctl.conf
   ```

3. **Real-time Scheduling**: Enable real-time priorities
   ```bash
   echo '@video soft rtprio 99' >> /etc/security/limits.conf
   echo '@video hard rtprio 99' >> /etc/security/limits.conf
   ```

### Application Tuning

- Adjust CPU affinity in the service manager script
- Configure DMA buffer pools for your hardware
- Tune AI inference batch sizes and intervals
- Optimize streaming bitrates and keyframe intervals

## Monitoring

### Statistics

Each service provides runtime statistics:

```bash
# Video capture stats
kill -USR1 $(pidof video_capture_main)

# AI processing frequency control
kill -USR1 $(pidof ai_processor_main)  # Reduce frequency
kill -USR2 $(pidof ai_processor_main)  # Increase frequency
```

### Logging

Logs are written to `/var/log/video_service/`:
- `video_capture.log` - Capture service logs
- `video_converter.log` - Converter service logs
- `ai_processor.log` - AI processor logs
- `cloud_streamer.log` - Streaming service logs

## Troubleshooting

### Common Issues

1. **Permission Denied**: Ensure proper permissions for video devices
   ```bash
   sudo usermod -a -G video $USER
   ```

2. **DMA Buffer Allocation Failed**: Check kernel support
   ```bash
   dmesg | grep -i dma
   ```

3. **AI Model Loading Failed**: Verify model path and format
   ```bash
   ls -la /opt/models/
   ```

4. **Streaming Connection Failed**: Check network connectivity
   ```bash
   telnet streaming-server.com 1935
   ```

### Debug Mode

Enable verbose logging:

```bash
export GST_DEBUG=3
export FASTDDS_LOG_LEVEL=Info
./video_service_manager.sh start
```

## Development

### Adding New AI Engines

1. Inherit from `AIEngine` base class
2. Implement `init()`, `process()`, and `cleanup()` methods
3. Register the engine in `AIProcessor::init()`

### Adding New Streaming Protocols

1. Extend `CloudStreamer` class
2. Implement protocol-specific pipeline creation
3. Add configuration options to `StreamConfig`

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## Support

For support and questions:
- Create an issue on GitHub
- Email: <EMAIL>
- Documentation: https://docs.example.com
