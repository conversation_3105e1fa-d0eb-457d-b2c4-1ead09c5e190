# Embedded Linux Video Service System

A high-performance, modular video processing system designed for embedded Linux environments. The system captures video from local V4L2 devices or remote RTSP cameras, processes frames through AI inference, and streams to cloud platforms via WebRTC or RTMP.

## Project Structure

```
video_service/
├── src/                    # Source files
│   ├── video_capture_main.cpp
│   ├── video_converter_main.cpp
│   ├── ai_processor_main.cpp
│   ├── cloud_streamer_main.cpp
│   ├── rtsp_server_main.cpp      # 🆕 RTSP服务器主程序
│   └── rtsp_server.cpp           # 🆕 RTSP服务器实现
├── include/                # Header files
│   ├── common.h
│   ├── dds_interface.h
│   ├── video_capture.h
│   ├── video_converter.h
│   ├── ai_processor.h
│   ├── cloud_streamer.h
│   └── rtsp_server.h             # 🆕 RTSP服务器头文件
├── config/                 # Configuration files
│   ├── config.json
│   ├── rtsp_server.json          # 🆕 RTSP服务器配置
│   ├── video_service.service.in
│   └── *.xml, *.idl
├── scripts/                # Management scripts
│   ├── video_service_manager.sh
│   ├── monitor_video_service.sh
│   ├── test_video_service.sh
│   ├── debug_video_service.sh
│   ├── setup_permissions.sh
│   ├── start_rtsp_server.sh      # 🆕 RTSP服务器启动脚本
│   └── compile_rtsp_server.sh    # 🆕 RTSP服务器编译脚本
├── test/                   # Test files
├── install/                # Installation files
├── docs/                   # Documentation
│   └── README.md
├── CMakeLists.txt          # CMake build configuration
└── Makefile               # Alternative build system
```

## Quick Start

### 1. Setup Environment
```bash
# Set up permissions and system configuration
chmod +x scripts/setup_permissions.sh
sudo scripts/setup_permissions.sh
```

### 2. Build the System

**Using CMake (Recommended):**
```bash
mkdir build && cd build
cmake ..
make -j$(nproc)
```

**Using Makefile:**
```bash
make check-deps  # Check dependencies
make -j$(nproc)  # Build
```

### 3. Configure the System
```bash
# Edit configuration file
nano config/config.json
```

### 4. Test the System
```bash
scripts/test_video_service.sh
```

### 5. Run the Services
```bash
# Start all services
sudo scripts/video_service_manager.sh start

# Check status
scripts/video_service_manager.sh status

# View logs
scripts/video_service_manager.sh logs video_capture
```

## Architecture

The system consists of five main components:

1. **Video Capture Service** (`src/video_capture_main.cpp`)
   - Captures video from V4L2 devices or RTSP streams
   - Hardware acceleration support (DMA buffers, V4L2 M2M)
   - Dynamic frame rate control

2. **Video Converter Service** (`src/video_converter_main.cpp`)
   - Converts and splits video streams for AI and cloud processing
   - Hardware-accelerated format conversion
   - Parallel processing pipeline

3. **AI Processor Service** (`src/ai_processor_main.cpp`)
   - Real-time AI inference on video frames
   - Support for TensorRT and ONNX Runtime
   - Dynamic processing frequency control

4. **Cloud Streamer Service** (`src/cloud_streamer_main.cpp`)
   - Streams processed video to cloud platforms
   - WebRTC and RTMP protocol support
   - Adaptive bitrate streaming

5. **RTSP Server Service** (`src/rtsp_server_main.cpp`) 🆕
   - Provides RTSP video streaming service for multiple clients
   - Automatic format conversion to standard 1280x720@30fps H.264
   - Hardware/software encoder selection with automatic fallback
   - Zero-copy optimization for minimal latency
   - Real-time performance monitoring and statistics
   - Flexible configuration via JSON files and command-line parameters

All components communicate via DDS (Data Distribution Service) for low-latency, real-time data exchange.

## Dependencies

### Required
- **FFmpeg** (>= 4.0) - Video processing and RTSP support
- **GStreamer** (>= 1.16) - Media pipeline and WebRTC/RTMP streaming
- **GStreamer RTSP Server** (>= 1.16) - RTSP server implementation 🆕
- **Fast DDS** (>= 2.0) - Inter-process communication
- **Linux Kernel** (>= 4.14) - V4L2 and DMA buffer support

### Optional
- **TensorRT** (>= 8.0) - NVIDIA GPU acceleration for AI inference
- **ONNX Runtime** (>= 1.8) - Cross-platform AI inference
- **OpenCV** (>= 4.0) - Additional image processing utilities
- **JsonCpp** (>= 1.7) - JSON configuration file support for RTSP server 🆕

## Management Scripts

### Service Manager (`scripts/video_service_manager.sh`)
```bash
scripts/video_service_manager.sh {start|stop|restart|status|logs <service>}
```

### System Monitor (`scripts/monitor_video_service.sh`)
```bash
scripts/monitor_video_service.sh {report|monitor [interval]|alerts|dds|network}
```

### Testing (`scripts/test_video_service.sh`)
```bash
scripts/test_video_service.sh [all|deps|build|capture|converter|ai|streamer|integration]
```

### Debugging (`scripts/debug_video_service.sh`)
```bash
scripts/debug_video_service.sh [system|dds|pipeline|trace|profile|network|report|interactive]
```

## Configuration

The main configuration file is `config/config.json`. Key sections include:

- `video_capture`: Source configuration (V4L2/RTSP)
- `ai_processor`: AI engine and model settings
- `cloud_streamer`: Streaming protocol and endpoints
- `system`: Resource allocation and performance tuning

## Installation

### System Installation
```bash
# Using CMake
cd build
sudo make install

# Using Makefile
sudo make install
```

### Systemd Service
```bash
sudo systemctl enable video_service
sudo systemctl start video_service
```

## Development

### Adding New Features
1. Add header files to `include/`
2. Add source files to `src/`
3. Update `CMakeLists.txt` or `Makefile`
4. Add tests to `test/`
5. Update documentation in `docs/`

### Testing
```bash
# Run all tests
scripts/test_video_service.sh

# Run specific test category
scripts/test_video_service.sh capture
```

### Debugging
```bash
# Interactive debugging session
scripts/debug_video_service.sh

# Generate debug report
scripts/debug_video_service.sh report
```

## RTSP Server Usage 🆕

### Quick Start
```bash
# Compile RTSP server
./scripts/compile_rtsp_server.sh rtsp_server

# Start with default configuration
./scripts/start_rtsp_server.sh start

# Access RTSP stream
ffplay rtsp://localhost:8554/stream
vlc rtsp://localhost:8554/stream
```

### Advanced Configuration
```bash
# Start with custom parameters
./scripts/start_rtsp_server.sh start \
    --topic CloudFrames \
    --port 8555 \
    --mount /ai \
    --width 1920 \
    --height 1080 \
    --bitrate 5000000

# Use JSON configuration file
./build/rtsp_server_main --config config/rtsp_server.json

# Monitor server status
./scripts/start_rtsp_server.sh status
./scripts/start_rtsp_server.sh logs
```

### Multiple Stream Setup
```bash
# Main stream - high quality
./build/rtsp_server_main --topic VideoFrames --port 8554 --mount /main --bitrate 5000000 &

# AI stream - medium quality
./build/rtsp_server_main --topic AIFrames --port 8555 --mount /ai --width 640 --height 640 &

# Cloud stream - optimized for streaming
./build/rtsp_server_main --topic CloudFrames --port 8556 --mount /cloud --hw-encoder &
```

For detailed RTSP server documentation, see `docs/rtsp_server_guide.md`.

## Performance Tuning

See `docs/README.md` for detailed performance tuning guidelines including:
- CPU isolation and affinity settings
- Memory configuration and DMA buffers
- Real-time scheduling priorities
- Network optimization

## Support

- **Documentation**: `docs/README.md`
- **Configuration**: `config/config.json`
- **Logs**: `/var/log/video_service/`
- **Issues**: Create GitHub issues for bug reports

## License

This project is licensed under the MIT License - see the LICENSE file for details.
