# Embedded Linux Video Service System

A high-performance, modular video processing system designed for embedded Linux environments. The system captures video from local V4L2 devices or remote RTSP cameras, processes frames through AI inference, and streams to cloud platforms via WebRTC or RTMP.

## Project Structure

```
video_service/
├── src/                    # Source files
│   ├── video_capture_main.cpp
│   ├── video_converter_main.cpp
│   ├── ai_processor_main.cpp
│   └── cloud_streamer_main.cpp
├── include/                # Header files
│   ├── common.h
│   ├── dds_interface.h
│   ├── video_capture.h
│   ├── video_converter.h
│   ├── ai_processor.h
│   └── cloud_streamer.h
├── config/                 # Configuration files
│   ├── config.json
│   ├── video_service.service.in
│   └── *.xml, *.idl
├── scripts/                # Management scripts
│   ├── video_service_manager.sh
│   ├── monitor_video_service.sh
│   ├── test_video_service.sh
│   ├── debug_video_service.sh
│   └── setup_permissions.sh
├── test/                   # Test files
├── install/                # Installation files
├── docs/                   # Documentation
│   └── README.md
├── CMakeLists.txt          # CMake build configuration
└── Makefile               # Alternative build system
```

## Quick Start

### 1. Setup Environment
```bash
# Set up permissions and system configuration
chmod +x scripts/setup_permissions.sh
sudo scripts/setup_permissions.sh
```

### 2. Build the System

**Using CMake (Recommended):**
```bash
mkdir build && cd build
cmake ..
make -j$(nproc)
```

**Using Makefile:**
```bash
make check-deps  # Check dependencies
make -j$(nproc)  # Build
```

### 3. Configure the System
```bash
# Edit configuration file
nano config/config.json
```

### 4. Test the System
```bash
scripts/test_video_service.sh
```

### 5. Run the Services
```bash
# Start all services
sudo scripts/video_service_manager.sh start

# Check status
scripts/video_service_manager.sh status

# View logs
scripts/video_service_manager.sh logs video_capture
```

## Architecture

The system consists of four main components:

1. **Video Capture Service** (`src/video_capture_main.cpp`)
   - Captures video from V4L2 devices or RTSP streams
   - Hardware acceleration support (DMA buffers, V4L2 M2M)
   - Dynamic frame rate control

2. **Video Converter Service** (`src/video_converter_main.cpp`)
   - Converts and splits video streams for AI and cloud processing
   - Hardware-accelerated format conversion
   - Parallel processing pipeline

3. **AI Processor Service** (`src/ai_processor_main.cpp`)
   - Real-time AI inference on video frames
   - Support for TensorRT and ONNX Runtime
   - Dynamic processing frequency control

4. **Cloud Streamer Service** (`src/cloud_streamer_main.cpp`)
   - Streams processed video to cloud platforms
   - WebRTC and RTMP protocol support
   - Adaptive bitrate streaming

All components communicate via DDS (Data Distribution Service) for low-latency, real-time data exchange.

## Dependencies

### Required
- **FFmpeg** (>= 4.0) - Video processing and RTSP support
- **GStreamer** (>= 1.16) - Media pipeline and WebRTC/RTMP streaming
- **Fast DDS** (>= 2.0) - Inter-process communication
- **Linux Kernel** (>= 4.14) - V4L2 and DMA buffer support

### Optional
- **TensorRT** (>= 8.0) - NVIDIA GPU acceleration for AI inference
- **ONNX Runtime** (>= 1.8) - Cross-platform AI inference
- **OpenCV** (>= 4.0) - Additional image processing utilities

## Management Scripts

### Service Manager (`scripts/video_service_manager.sh`)
```bash
scripts/video_service_manager.sh {start|stop|restart|status|logs <service>}
```

### System Monitor (`scripts/monitor_video_service.sh`)
```bash
scripts/monitor_video_service.sh {report|monitor [interval]|alerts|dds|network}
```

### Testing (`scripts/test_video_service.sh`)
```bash
scripts/test_video_service.sh [all|deps|build|capture|converter|ai|streamer|integration]
```

### Debugging (`scripts/debug_video_service.sh`)
```bash
scripts/debug_video_service.sh [system|dds|pipeline|trace|profile|network|report|interactive]
```

## Configuration

The main configuration file is `config/config.json`. Key sections include:

- `video_capture`: Source configuration (V4L2/RTSP)
- `ai_processor`: AI engine and model settings
- `cloud_streamer`: Streaming protocol and endpoints
- `system`: Resource allocation and performance tuning

## Installation

### System Installation
```bash
# Using CMake
cd build
sudo make install

# Using Makefile
sudo make install
```

### Systemd Service
```bash
sudo systemctl enable video_service
sudo systemctl start video_service
```

## Development

### Adding New Features
1. Add header files to `include/`
2. Add source files to `src/`
3. Update `CMakeLists.txt` or `Makefile`
4. Add tests to `test/`
5. Update documentation in `docs/`

### Testing
```bash
# Run all tests
scripts/test_video_service.sh

# Run specific test category
scripts/test_video_service.sh capture
```

### Debugging
```bash
# Interactive debugging session
scripts/debug_video_service.sh

# Generate debug report
scripts/debug_video_service.sh report
```

## Performance Tuning

See `docs/README.md` for detailed performance tuning guidelines including:
- CPU isolation and affinity settings
- Memory configuration and DMA buffers
- Real-time scheduling priorities
- Network optimization

## Support

- **Documentation**: `docs/README.md`
- **Configuration**: `config/config.json`
- **Logs**: `/var/log/video_service/`
- **Issues**: Create GitHub issues for bug reports

## License

This project is licensed under the MIT License - see the LICENSE file for details.
