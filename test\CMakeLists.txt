# Test programs for Video Service

cmake_minimum_required(VERSION 3.16)
project(VideoServiceTest VERSION 1.0.1 LANGUAGES CXX)


# Include parent directories
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/../include)

# Find FFmpeg
find_package(PkgConfig REQUIRED)
pkg_check_modules(FFMPEG REQUIRED
    libavformat>=58.0
    libavcodec>=58.0
    libavutil>=56.0
)

# Test libraries
set(FFMPEG_TEST_LIBS
    ${FFMPEG_LIBRARIES}
    pthread
)

get_filename_component(EXT_LIB_PATH ${CMAKE_CURRENT_SOURCE_DIR}/../dds_video_frame ABSOLUTE)
add_subdirectory(${EXT_LIB_PATH} ${CMAKE_BINARY_DIR}/build)

# Include directories
include_directories(${FFMPEG_INCLUDE_DIRS})

# Link directories
link_directories(${FFMPEG_LIBRARY_DIRS})

# RTSP FFmpeg test (encoded packets only)
add_executable(test_rtsp_ffmpeg
    test_rtsp_ffmpeg.cpp
)

target_link_libraries(test_rtsp_ffmpeg ${FFMPEG_TEST_LIBS} DDSVideoFrame_lib_${PROJECT_NAME})

# Set C++ standard
set_property(TARGET test_rtsp_ffmpeg PROPERTY CXX_STANDARD 17)

# Add test targets
add_custom_target(run_ffmpeg_test
    COMMAND echo "Running RTSP FFmpeg test..."
    COMMAND echo "Usage: ./test_rtsp_ffmpeg <rtsp_url> [tcp]"
    DEPENDS test_rtsp_ffmpeg
)