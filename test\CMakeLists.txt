# Test programs for Video Service

cmake_minimum_required(VERSION 3.16)

# Include parent directories
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/../include)

# Find FFmpeg
find_package(PkgConfig REQUIRED)
pkg_check_modules(FFMPEG REQUIRED
    libavformat>=58.0
    libavcodec>=58.0
    libavutil>=56.0
)

# Find GStreamer
pkg_check_modules(GSTREAMER REQUIRED
    gstreamer-1.0>=1.16
    gstreamer-app-1.0
    gstreamer-video-1.0
    gstreamer-rtsp-1.0
    gstreamer-plugins-base-1.0
)

# Test libraries
set(FFMPEG_TEST_LIBS
    ${FFMPEG_LIBRARIES}
    pthread
)

set(GSTREAMER_TEST_LIBS
    ${GSTREAMER_LIBRARIES}
    pthread
)

# Include directories
include_directories(${FFMPEG_INCLUDE_DIRS})
include_directories(${GSTREAMER_INCLUDE_DIRS})

# Link directories
link_directories(${FFMPEG_LIBRARY_DIRS})
link_directories(${GSTREAMER_LIBRARY_DIRS})

# RTSP FFmpeg test (encoded packets only)
add_executable(test_rtsp_ffmpeg
    test_rtsp_ffmpeg.cpp
)

target_link_libraries(test_rtsp_ffmpeg ${FFMPEG_TEST_LIBS})

# Set C++ standard
set_property(TARGET test_rtsp_ffmpeg PROPERTY CXX_STANDARD 17)

# RTSP GStreamer test
add_executable(test_rtsp_gstreamer
    test_rtsp_gstreamer.cpp
)

target_link_libraries(test_rtsp_gstreamer ${GSTREAMER_TEST_LIBS})

# Set C++ standard
set_property(TARGET test_rtsp_gstreamer PROPERTY CXX_STANDARD 17)

# Install test programs
install(TARGETS test_rtsp_ffmpeg test_rtsp_gstreamer
    RUNTIME DESTINATION bin/test
)

# Add test targets
add_custom_target(run_ffmpeg_test
    COMMAND echo "Running RTSP FFmpeg test..."
    COMMAND echo "Usage: ./test_rtsp_ffmpeg <rtsp_url> [tcp]"
    DEPENDS test_rtsp_ffmpeg
)

add_custom_target(run_gstreamer_test
    COMMAND echo "Running RTSP GStreamer test..."
    COMMAND echo "Usage: ./test_rtsp_gstreamer <rtsp_url> [tcp] [sw_decode]"
    DEPENDS test_rtsp_gstreamer
)
