#include "common.h"
#include "rtsp_server.h"
#include "capture_config.h"
#include <sstream>
#include <iomanip>
#include <thread>

// VideoFormatConverter 实现
bool VideoFormatConverter::init(int input_width, int input_height, int input_format,
                               int output_width, int output_height, int output_fps,
                               bool use_hardware) {
    input_width_ = input_width;
    input_height_ = input_height;
    input_format_ = input_format;
    output_width_ = output_width;
    output_height_ = output_height;
    output_fps_ = output_fps;
    use_hardware_ = use_hardware;
    
    // 创建pipeline
    pipeline_ = gst_pipeline_new("converter-pipeline");
    if (!pipeline_) {
        LOG_E("Failed to create converter pipeline");
        return false;
    }
    
    // 创建元素
    appsrc_ = gst_element_factory_make("appsrc", "source");
    videoconvert_ = gst_element_factory_make("videoconvert", "convert");
    videoscale_ = gst_element_factory_make("videoscale", "scale");
    capsfilter_input_ = gst_element_factory_make("capsfilter", "input-caps");
    capsfilter_output_ = gst_element_factory_make("capsfilter", "output-caps");
    appsink_ = gst_element_factory_make("appsink", "sink");
    
    if (!appsrc_ || !videoconvert_ || !videoscale_ || !capsfilter_input_ || 
        !capsfilter_output_ || !appsink_) {
        LOG_E("Failed to create converter elements");
        cleanup();
        return false;
    }
    
    // 选择编码器 - 优先使用mpph264enc，回退到mpph265enc，最后使用软件编码器
    const char* encoder_name = nullptr;
    if (use_hardware_) {
        encoder_name = "mpph264enc";
        encoder_ = gst_element_factory_make(encoder_name, "encoder");
        if (!encoder_) {
            LOG_W("mpph264enc not available, trying mpph265enc");
            encoder_name = "mpph265enc";
            encoder_ = gst_element_factory_make(encoder_name, "encoder");
        }
        if (!encoder_) {
            LOG_W("Hardware encoders not available, falling back to software");
            encoder_name = "x264enc";
            encoder_ = gst_element_factory_make(encoder_name, "encoder");
            use_hardware_ = false;
        }
    } else {
        encoder_name = "x264enc";
        encoder_ = gst_element_factory_make(encoder_name, "encoder");
    }
    
    if (!encoder_) {
        LOG_E("Failed to create encoder");
        cleanup();
        return false;
    }
    
    // 配置appsrc
    g_object_set(G_OBJECT(appsrc_),
                 "caps", gst_caps_new_simple("video/x-raw",
                                           "format", G_TYPE_STRING, "I420",
                                           "width", G_TYPE_INT, input_width_,
                                           "height", G_TYPE_INT, input_height_,
                                           "framerate", GST_TYPE_FRACTION, output_fps_, 1,
                                           NULL),
                 "stream-type", 0, // GST_APP_STREAM_TYPE_STREAM
                 "format", GST_FORMAT_TIME,
                 "is-live", TRUE,
                 "do-timestamp", TRUE,
                 NULL);
    
    // 配置编码器
    if (use_hardware_) {
        // 使用mpph264enc或mpph265enc硬件编码器
        g_object_set(G_OBJECT(encoder_),
                     "bps", 2000000,        // 2Mbps in bps
                     "bps-min", 1000000,    // 1Mbps minimum
                     "bps-max", 4000000,    // 4Mbps maximum
                     "profile", "baseline", // baseline profile
                     "gop", 15,
                     "rc-mode", 1,          // CBR
                     NULL);
    } else {
        g_object_set(G_OBJECT(encoder_),
                     "bitrate", 2000,
                     "tune", 0x00000004, // zerolatency
                     "speed-preset", 1,  // ultrafast
                     NULL);
    }
    
    // 配置输出caps
    GstCaps* output_caps = gst_caps_new_simple("video/x-raw",
                                              "format", G_TYPE_STRING, "I420",
                                              "width", G_TYPE_INT, output_width_,
                                              "height", G_TYPE_INT, output_height_,
                                              "framerate", GST_TYPE_FRACTION, output_fps_, 1,
                                              NULL);
    g_object_set(G_OBJECT(capsfilter_output_), "caps", output_caps, NULL);
    gst_caps_unref(output_caps);
    
    // 配置appsink
    g_object_set(G_OBJECT(appsink_),
                 "emit-signals", TRUE,
                 "sync", FALSE,
                 "max-buffers", 1,
                 "drop", TRUE,
                 NULL);
    
    // 添加元素到pipeline
    gst_bin_add_many(GST_BIN(pipeline_), appsrc_, videoconvert_, videoscale_,
                     capsfilter_output_, encoder_, appsink_, NULL);
    
    // 链接元素
    if (!gst_element_link_many(appsrc_, videoconvert_, videoscale_,
                              capsfilter_output_, encoder_, appsink_, NULL)) {
        LOG_E("Failed to link converter elements");
        cleanup();
        return false;
    }
    
    // 启动pipeline
    GstStateChangeReturn ret = gst_element_set_state(pipeline_, GST_STATE_PLAYING);
    if (ret == GST_STATE_CHANGE_FAILURE) {
        LOG_E("Failed to start converter pipeline");
        cleanup();
        return false;
    }
    
    LOG_I("VideoFormatConverter initialized: %dx%d -> %dx%d@%dfps, HW=%s",
          input_width_, input_height_, output_width_, output_height_, output_fps_,
          use_hardware_ ? "yes" : "no");
    
    return true;
}

bool VideoFormatConverter::convert_frame(const Frame& input_frame, GstBuffer** output_buffer) {
    if (!pipeline_ || !appsrc_ || !appsink_) {
        return false;
    }
    
    auto start_time = std::chrono::high_resolution_clock::now();
    
    // 创建输入buffer
    GstBuffer* input_buffer = gst_buffer_new_allocate(NULL, input_frame.data.size(), NULL);
    if (!input_buffer) {
        LOG_E("Failed to allocate input buffer");
        return false;
    }
    
    // 填充数据 - 零拷贝优化
    GstMapInfo map;
    if (!gst_buffer_map(input_buffer, &map, GST_MAP_WRITE)) {
        gst_buffer_unref(input_buffer);
        return false;
    }
    
    memcpy(map.data, input_frame.data.data(), input_frame.data.size());
    gst_buffer_unmap(input_buffer, &map);
    
    // 设置时间戳
    GST_BUFFER_PTS(input_buffer) = input_frame.timestamp * 1000; // us to ns
    GST_BUFFER_DTS(input_buffer) = GST_BUFFER_PTS(input_buffer);
    
    // 推送到appsrc
    GstFlowReturn flow_ret = gst_app_src_push_buffer(GST_APP_SRC(appsrc_), input_buffer);
    if (flow_ret != GST_FLOW_OK) {
        LOG_E("Failed to push buffer to appsrc: %d", flow_ret);
        return false;
    }
    
    // 从appsink拉取转换后的buffer
    GstSample* sample = gst_app_sink_try_pull_sample(GST_APP_SINK(appsink_), GST_SECOND);
    if (!sample) {
        LOG_W("Failed to pull converted sample");
        return false;
    }
    
    *output_buffer = gst_buffer_ref(gst_sample_get_buffer(sample));
    gst_sample_unref(sample);
    
    // 更新统计信息
    auto end_time = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end_time - start_time);
    
    frames_converted_.fetch_add(1);
    conversion_time_us_.fetch_add(duration.count());
    
    return true;
}

void VideoFormatConverter::cleanup() {
    if (pipeline_) {
        gst_element_set_state(pipeline_, GST_STATE_NULL);
        gst_object_unref(pipeline_);
        pipeline_ = nullptr;
    }
    
    // 元素会随pipeline一起释放
    appsrc_ = nullptr;
    appsink_ = nullptr;
    videoconvert_ = nullptr;
    videoscale_ = nullptr;
    encoder_ = nullptr;
    capsfilter_input_ = nullptr;
    capsfilter_output_ = nullptr;
}

// RTSPMediaFactory 实现
RTSPMediaFactory::RTSPMediaFactory(const RTSPServerConfig& config) : config_(config), factory_(nullptr) {
}

RTSPMediaFactory::~RTSPMediaFactory() {
    cleanup();
}

bool RTSPMediaFactory::init() {
    // 创建GStreamer媒体工厂
    factory_ = gst_rtsp_media_factory_new();
    if (!factory_) {
        LOG_E("Failed to create GStreamer media factory");
        return false;
    }

    // 设置为可共享的工厂
    gst_rtsp_media_factory_set_shared(factory_, TRUE);
    gst_rtsp_media_factory_set_eos_shutdown(factory_, TRUE);

    // 设置UDP和组播协议
    GstRTSPLowerTrans protocols = GstRTSPLowerTrans(GST_RTSP_LOWER_TRANS_UDP | GST_RTSP_LOWER_TRANS_UDP_MCAST);
    gst_rtsp_media_factory_set_protocols(factory_, protocols);

    // 初始化DDS读取器
    dds_reader_ = std::make_shared<DDSVideoReader>(config_.dds_topic, config_.buffer_size);
    if (!dds_reader_) {
        LOG_E("Failed to create DDS video reader for topic: %s", config_.dds_topic.c_str());
        return false;
    }

    // 等待第一帧数据来确定视频格式
    if (!wait_for_first_frame()) {
        LOG_E("Failed to receive first frame from DDS topic: %s", config_.dds_topic.c_str());
        return false;
    }

    // 初始化视频格式转换器
    converter_ = std::make_unique<VideoFormatConverter>();

    // 根据接收到的视频格式创建pipeline描述
    std::string pipeline_desc = create_pipeline_description();
    gst_rtsp_media_factory_set_launch(factory_, pipeline_desc.c_str());

    // 设置媒体配置回调
    g_signal_connect(factory_, "media-configure", G_CALLBACK(media_configure_callback), this);

    LOG_I("RTSPMediaFactory initialized for topic: %s", config_.dds_topic.c_str());
    LOG_I("Pipeline: %s", pipeline_desc.c_str());

    return true;
}

bool RTSPMediaFactory::wait_for_first_frame() {
    LOG_I("Waiting for first frame from DDS topic: %s", config_.dds_topic.c_str());

    Frame first_frame;
    int retry_count = 0;
    const int max_retries = 50; // 最多等待5秒 (50 * 100ms)

    while (retry_count < max_retries) {
        if (dds_reader_->read(first_frame, 100)) { // 100ms超时
            // 成功接收到第一帧，更新配置参数
            current_width_.store(first_frame.width);
            current_height_.store(first_frame.height);
            current_format_.store(first_frame.format);

            // 根据接收到的视频格式调整输出参数
            if (config_.output_width == 0 || config_.output_height == 0) {
                // 如果没有指定输出尺寸，使用输入尺寸
                config_.output_width = first_frame.width;
                config_.output_height = first_frame.height;
            }

            LOG_I("First frame received: %dx%d format=%d, output will be: %dx%d@%dfps",
                  first_frame.width, first_frame.height, first_frame.format,
                  config_.output_width, config_.output_height, config_.output_fps);

            return true;
        }

        retry_count++;
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }

    LOG_E("Timeout waiting for first frame from DDS topic: %s", config_.dds_topic.c_str());
    return false;
}

std::string RTSPMediaFactory::create_pipeline_description() {
    std::ostringstream pipeline;

    // 使用从DDS接收到的实际视频参数
    int input_width = current_width_.load();
    int input_height = current_height_.load();

    // 使用appsrc作为数据源，caps基于实际接收到的视频格式
    pipeline << "( appsrc name=source is-live=true do-timestamp=true format=time ";
    pipeline << "max-bytes=0 block=false ";
    pipeline << "caps=\"video/x-raw,format=I420,width=" << input_width;
    pipeline << ",height=" << input_height;
    pipeline << ",framerate=" << config_.output_fps << "/1\" ";

    // 添加队列缓冲
    pipeline << "! queue max-size-buffers=2 max-size-time=0 max-size-bytes=0 ";

    // 如果输入和输出尺寸不同，添加缩放
    if (input_width != config_.output_width || input_height != config_.output_height) {
        pipeline << "! videoscale ! video/x-raw,width=" << config_.output_width;
        pipeline << ",height=" << config_.output_height << " ";
    }

    // 编码器选择
    if (config_.use_hardware_encoder) {
        // 优先使用mpph264enc
        if (RTSPServerUtils::check_hardware_encoder_support("H264")) {
            pipeline << "! mpph264enc bps=" << config_.output_bitrate;
            pipeline << " bps-min=" << (config_.output_bitrate / 2);
            pipeline << " bps-max=" << (config_.output_bitrate * 2);
            pipeline << " profile=baseline gop=" << config_.gop_size;
            pipeline << " rc-mode=1";  // CBR
            pipeline << " ! h264parse ! rtph264pay name=pay0 pt=96 config-interval=1 mtu=1400 )";
        } else if (RTSPServerUtils::check_hardware_encoder_support("H265")) {
            // 回退到mpph265enc
            pipeline << "! mpph265enc bps=" << config_.output_bitrate;
            pipeline << " bps-min=" << (config_.output_bitrate / 2);
            pipeline << " bps-max=" << (config_.output_bitrate * 2);
            pipeline << " profile=baseline gop=" << config_.gop_size;
            pipeline << " rc-mode=1";  // CBR
            pipeline << " ! h265parse ! rtph265pay name=pay0 pt=96 config-interval=1 mtu=1400 )";
        } else {
            // 硬件编码器不可用，使用软件编码器
            pipeline << "! x264enc bitrate=" << (config_.output_bitrate / 1000);
            pipeline << " tune=zerolatency speed-preset=ultrafast";
            pipeline << " key-int-max=" << config_.gop_size;
            pipeline << " threads=4";
            pipeline << " ! h264parse ! rtph264pay name=pay0 pt=96 config-interval=1 mtu=1400 )";
        }
    } else {
        // 软件编码器
        pipeline << "! x264enc bitrate=" << (config_.output_bitrate / 1000);
        pipeline << " tune=zerolatency speed-preset=ultrafast";
        pipeline << " key-int-max=" << config_.gop_size;
        pipeline << " threads=4";
        pipeline << " ! h264parse ! rtph264pay name=pay0 pt=96 config-interval=1 mtu=1400 )";
    }
    
    return pipeline.str();
}

void RTSPMediaFactory::cleanup() {
    converter_.reset();
    dds_reader_.reset();

    if (factory_) {
        g_object_unref(factory_);
        factory_ = nullptr;
    }
}

void RTSPMediaFactory::configure_media(GstRTSPMedia* media) {
    // 获取媒体的pipeline
    GstElement* pipeline = gst_rtsp_media_get_element(media);
    if (!pipeline) {
        LOG_E("Failed to get media pipeline");
        return;
    }

    // 查找appsrc元素
    GstElement* appsrc = gst_bin_get_by_name(GST_BIN(pipeline), "source");
    if (!appsrc) {
        LOG_E("Failed to find appsrc element");
        gst_object_unref(pipeline);
        return;
    }

    // 设置appsrc回调
    g_signal_connect(appsrc, "need-data", G_CALLBACK(need_data_callback), this);
    g_signal_connect(appsrc, "enough-data", G_CALLBACK(enough_data_callback), this);

    // 配置appsrc属性
    g_object_set(G_OBJECT(appsrc),
                 "is-live", TRUE,
                 "do-timestamp", TRUE,
                 "format", GST_FORMAT_TIME,
                 "max-bytes", 0,
                 "block", FALSE,
                 NULL);

    gst_object_unref(appsrc);
    gst_object_unref(pipeline);

    LOG_I("Media configured successfully");
}

void RTSPMediaFactory::feed_data(GstElement* appsrc) {
    if (!dds_reader_ || !converter_) {
        LOG_E("DDS reader or converter not initialized");
        return;
    }

    // 从DDS读取视频帧
    Frame input_frame;
    if (!dds_reader_->read(input_frame, 100)) {  // 100ms超时
        // 没有数据可读，发送EOS或等待
        LOG_D("No DDS data available");
        return;
    }

    // 检查是否需要更新转换器
    if (update_converter_if_needed(input_frame)) {
        LOG_I("Video format changed, converter updated");
    }

    // 转换视频帧格式
    GstBuffer* output_buffer = nullptr;
    if (!converter_->convert_frame(input_frame, &output_buffer)) {
        LOG_E("Failed to convert video frame");
        return;
    }

    if (!output_buffer) {
        LOG_E("Converted buffer is null");
        return;
    }

    // 推送到appsrc
    GstFlowReturn flow_ret = gst_app_src_push_buffer(GST_APP_SRC(appsrc), output_buffer);
    if (flow_ret != GST_FLOW_OK) {
        LOG_E("Failed to push buffer to appsrc: %d", flow_ret);
        gst_buffer_unref(output_buffer);
        return;
    }

    // 更新统计信息
    frames_served_.fetch_add(1);

    LOG_D("Frame fed to appsrc successfully");
}

bool RTSPMediaFactory::update_converter_if_needed(const Frame& frame) {
    bool format_changed = false;

    // 检查视频参数是否改变
    if (current_width_.load() != frame.width ||
        current_height_.load() != frame.height ||
        current_format_.load() != frame.format) {

        current_width_.store(frame.width);
        current_height_.store(frame.height);
        current_format_.store(frame.format);
        format_changed = true;
    }

    // 如果格式改变或转换器未初始化，重新初始化转换器
    if (format_changed || !converter_) {
        if (!converter_) {
            converter_ = std::make_unique<VideoFormatConverter>();
        }

        // 重新初始化转换器
        converter_->cleanup();
        if (!converter_->init(frame.width, frame.height, frame.format,
                             config_.output_width, config_.output_height,
                             config_.output_fps, config_.use_hardware_encoder)) {
            LOG_E("Failed to reinitialize converter");
            return false;
        }

        LOG_I("Converter updated: %dx%d (%d) -> %dx%d@%dfps",
              frame.width, frame.height, frame.format,
              config_.output_width, config_.output_height, config_.output_fps);
    }

    return format_changed;
}

// RTSPServerService 实现
bool RTSPServerService::init(const RTSPServerConfig& config) {
    config_ = config;
    
    // 初始化GStreamer
    if (!RTSPServerUtils::init_gstreamer()) {
        LOG_E("Failed to initialize GStreamer");
        return false;
    }
    
    // 创建RTSP服务器
    server_ = gst_rtsp_server_new();
    if (!server_) {
        LOG_E("Failed to create RTSP server");
        return false;
    }
    
    // 配置服务器
    gst_rtsp_server_set_address(server_, config_.server_address.c_str());
    gst_rtsp_server_set_service(server_, std::to_string(config_.server_port).c_str());
    
    // 创建挂载点
    mounts_ = gst_rtsp_server_get_mount_points(server_);
    
    // 创建媒体工厂
    factory_ = std::make_unique<RTSPMediaFactory>(config_);
    if (!factory_->init()) {
        LOG_E("Failed to initialize media factory");
        return false;
    }
    
    // 挂载媒体工厂
    gst_rtsp_mount_points_add_factory(mounts_, config_.mount_point.c_str(),
                                     factory_->get_factory());
    
    // 配置传输参数
    RTSPServerUtils::configure_rtsp_transport_params(server_);
    RTSPServerUtils::set_rtsp_buffer_sizes(server_, config_.buffer_size);
    
    // 连接信号
    g_signal_connect(server_, "client-connected", 
                     G_CALLBACK(client_connected_callback), this);
    
    LOG_I("RTSP server initialized: %s:%d%s",
          config_.server_address.c_str(), config_.server_port, config_.mount_point.c_str());

    return true;
}

bool RTSPServerService::start() {
    if (running_.load()) {
        LOG_W("RTSP server already running");
        return true;
    }

    // 启动服务器
    guint server_id = gst_rtsp_server_attach(server_, NULL);
    if (server_id == 0) {
        LOG_E("Failed to attach RTSP server");
        return false;
    }

    stop_requested_.store(false);
    server_thread_ = std::thread(&RTSPServerService::run, this);
    running_.store(true);
    start_time_ = std::chrono::steady_clock::now();

    LOG_I("RTSP server started on %s:%d%s",
          config_.server_address.c_str(), config_.server_port, config_.mount_point.c_str());

    return true;
}

void RTSPServerService::stop() {
    if (!running_.load()) {
        return;
    }

    stop_requested_.store(true);

    if (server_thread_.joinable()) {
        server_thread_.join();
    }

    if (server_) {
        g_object_unref(server_);
        server_ = nullptr;
    }

    if (mounts_) {
        g_object_unref(mounts_);
        mounts_ = nullptr;
    }

    factory_.reset();
    running_.store(false);

    LOG_I("RTSP server stopped");
}

void RTSPServerService::run() {
    GMainLoop* loop = g_main_loop_new(NULL, FALSE);

    LOG_I("RTSP server main loop started");

    while (!stop_requested_.load()) {
        // 运行GLib主循环，处理RTSP连接
        g_main_context_iteration(g_main_loop_get_context(loop), FALSE);

        // 短暂休眠避免CPU占用过高
        std::this_thread::sleep_for(std::chrono::milliseconds(1));
    }

    g_main_loop_unref(loop);
    LOG_I("RTSP server main loop stopped");
}

RTSPServerService::ServerStats RTSPServerService::get_stats() const {
    ServerStats stats;
    stats.total_connections = total_connections_.load();
    stats.active_connections = active_connections_.load();
    stats.error_count = error_count_.load();

    if (factory_) {
        stats.frames_served = factory_->get_frames_served();
        stats.clients_connected = factory_->get_clients_connected();
        stats.avg_conversion_time_ms = 0.0; // TODO: 从converter获取
    }

    auto now = std::chrono::steady_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::seconds>(now - start_time_);
    stats.uptime_seconds = duration.count();

    {
        std::lock_guard<std::mutex> lock(const_cast<std::mutex&>(error_mutex_));
        stats.last_error = last_error_;
    }

    return stats;
}

void RTSPServerService::print_stats() const {
    auto stats = get_stats();

    LOG_I("=== RTSP Server Statistics ===");
    LOG_I("Uptime: %.1f seconds", stats.uptime_seconds);
    LOG_I("Total connections: %lu", stats.total_connections);
    LOG_I("Active connections: %lu", stats.active_connections);
    LOG_I("Frames served: %lu", stats.frames_served);
    LOG_I("Clients connected: %lu", stats.clients_connected);
    LOG_I("Error count: %lu", stats.error_count);
    if (!stats.last_error.empty()) {
        LOG_I("Last error: %s", stats.last_error.c_str());
    }
    if (stats.avg_conversion_time_ms > 0) {
        LOG_I("Avg conversion time: %.2f ms", stats.avg_conversion_time_ms);
    }
}

bool RTSPServerService::update_bitrate(int new_bitrate) {
    if (new_bitrate < config_.min_bitrate || new_bitrate > config_.max_bitrate) {
        LOG_W("Bitrate %d out of range [%d, %d]", new_bitrate,
              config_.min_bitrate, config_.max_bitrate);
        return false;
    }

    config_.output_bitrate = new_bitrate;
    LOG_I("Updated bitrate to %d bps", new_bitrate);
    return true;
}

bool RTSPServerService::update_quality(int new_gop_size) {
    if (new_gop_size < 1 || new_gop_size > 300) {
        LOG_W("GOP size %d out of range [1, 300]", new_gop_size);
        return false;
    }

    config_.gop_size = new_gop_size;
    LOG_I("Updated GOP size to %d", new_gop_size);
    return true;
}

void RTSPServerService::handle_client_connected() {
    total_connections_.fetch_add(1);
    active_connections_.fetch_add(1);
    LOG_I("Client connected, active: %lu, total: %lu",
          active_connections_.load(), total_connections_.load());
}

void RTSPServerService::handle_client_disconnected() {
    active_connections_.fetch_sub(1);
    LOG_I("Client disconnected, active: %lu", active_connections_.load());
}

// RTSPMediaFactory静态回调函数
void RTSPMediaFactory::media_configure_callback(GstRTSPMediaFactory* factory,
                                               GstRTSPMedia* media,
                                               gpointer user_data) {
    RTSPMediaFactory* media_factory = static_cast<RTSPMediaFactory*>(user_data);
    media_factory->configure_media(media);
}

void RTSPMediaFactory::need_data_callback(GstElement* appsrc, guint unused, gpointer user_data) {
    RTSPMediaFactory* media_factory = static_cast<RTSPMediaFactory*>(user_data);
    media_factory->feed_data(appsrc);
}

void RTSPMediaFactory::enough_data_callback(GstElement* appsrc, gpointer user_data) {
    // 暂停数据推送
    LOG_D("Enough data callback triggered");
}

// RTSPServerService静态回调函数
void RTSPServerService::client_connected_callback(GstRTSPServer* server,
                                                 GstRTSPClient* client,
                                                 gpointer user_data) {
    RTSPServerService* service = static_cast<RTSPServerService*>(user_data);
    service->handle_client_connected();
}

void RTSPServerService::client_disconnected_callback(GstRTSPServer* server,
                                                    GstRTSPClient* client,
                                                    gpointer user_data) {
    RTSPServerService* service = static_cast<RTSPServerService*>(user_data);
    service->handle_client_disconnected();
}

// RTSPServerUtils 实现
namespace RTSPServerUtils {

bool init_gstreamer() {
    static bool initialized = false;
    if (initialized) {
        return true;
    }

    GError* error = nullptr;
    if (!gst_init_check(nullptr, nullptr, &error)) {
        if (error) {
            LOG_E("Failed to initialize GStreamer: %s", error->message);
            g_error_free(error);
        } else {
            LOG_E("Failed to initialize GStreamer: unknown error");
        }
        return false;
    }

    initialized = true;
    LOG_I("GStreamer initialized successfully");
    return true;
}

std::string v4l2_format_to_gst_caps(int v4l2_format, int width, int height, int fps) {
    std::ostringstream caps;
    caps << "video/x-raw";

    switch (v4l2_format) {
        case V4L2_PIX_FMT_YUYV:
            caps << ",format=YUY2";
            break;
        case V4L2_PIX_FMT_NV12:
            caps << ",format=NV12";
            break;
        case V4L2_PIX_FMT_RGB24:
            caps << ",format=RGB";
            break;
        case V4L2_PIX_FMT_BGR24:
            caps << ",format=BGR";
            break;
        default:
            caps << ",format=I420"; // 默认格式
            break;
    }

    caps << ",width=" << width << ",height=" << height;
    caps << ",framerate=" << fps << "/1";

    return caps.str();
}

std::string create_encoder_pipeline(const RTSPServerConfig& config, bool use_hardware) {
    std::ostringstream pipeline;

    if (use_hardware && check_hardware_encoder_support(config.output_codec)) {
        if (config.output_codec == "H264") {
            pipeline << "mpph264enc bps=" << config.output_bitrate;
            pipeline << " bps-min=" << (config.output_bitrate / 2);
            pipeline << " bps-max=" << (config.output_bitrate * 2);
            pipeline << " profile=baseline rc-mode=1 gop=" << config.gop_size;
        } else if (config.output_codec == "H265") {
            pipeline << "mpph265enc bps=" << config.output_bitrate;
            pipeline << " bps-min=" << (config.output_bitrate / 2);
            pipeline << " bps-max=" << (config.output_bitrate * 2);
            pipeline << " profile=baseline rc-mode=1 gop=" << config.gop_size;
        }
    } else {
        if (config.output_codec == "H264") {
            pipeline << "x264enc bitrate=" << (config.output_bitrate / 1000);
            pipeline << " tune=zerolatency speed-preset=ultrafast";
            pipeline << " key-int-max=" << config.gop_size;
        } else if (config.output_codec == "H265") {
            pipeline << "x265enc bitrate=" << (config.output_bitrate / 1000);
            pipeline << " tune=zerolatency speed-preset=ultrafast";
            pipeline << " key-int-max=" << config.gop_size;
        }
    }

    pipeline << " key-int-max=" << config.gop_size;

    return pipeline.str();
}

void optimize_gst_pipeline_for_realtime(GstElement* pipeline) {
    // 设置pipeline为实时模式
    gst_pipeline_set_latency(GST_PIPELINE(pipeline), 0);

    // 禁用缓冲
    g_object_set(G_OBJECT(pipeline), "async-handling", TRUE, NULL);
}

bool check_hardware_encoder_support(const std::string& codec) {
    GstElementFactory* factory = nullptr;

    if (codec == "H264") {
        // 优先检查mpph264enc，回退到nvh264enc
        factory = gst_element_factory_find("mpph264enc");
        if (!factory) {
            factory = gst_element_factory_find("nvh264enc");
        }
    } else if (codec == "H265") {
        // 优先检查mpph265enc，回退到nvh265enc
        factory = gst_element_factory_find("mpph265enc");
        if (!factory) {
            factory = gst_element_factory_find("nvh265enc");
        }
    }

    if (factory) {
        gst_object_unref(factory);
        return true;
    }

    return false;
}

void configure_rtsp_transport_params(GstRTSPServer* server) {
    // 配置传输参数以优化实时性
    g_object_set(G_OBJECT(server),
                 "max-session-timeout", 60,  // 60秒会话超时
                 NULL);
}

void set_rtsp_buffer_sizes(GstRTSPServer* server, int buffer_size) {
    // 设置缓冲区大小
    // 注意：这些参数可能需要根据GStreamer版本调整
    g_object_set(G_OBJECT(server),
                 "backlog", buffer_size,
                 NULL);
}

} // namespace RTSPServerUtils
