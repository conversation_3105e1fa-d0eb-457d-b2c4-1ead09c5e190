#include "common.h"
#include "rtsp_server.h"
#include <iostream>
#include <thread>
#include <chrono>

int main() {
    // 初始化GStreamer
    gst_init(nullptr, nullptr);
    
    // 创建RTSP服务器配置
    RTSPServerConfig config;
    config.dds_topic = "TestVideoFrames";
    config.server_address = "0.0.0.0";
    config.server_port = 8554;
    config.mount_point = "/test";
    config.output_width = 1280;
    config.output_height = 720;
    config.output_fps = 30;
    config.output_bitrate = 2000000;
    config.use_hardware_encoder = false; // 使用软件编码器进行测试
    config.buffer_size = 5;
    
    std::cout << "Creating RTSP server service..." << std::endl;
    
    // 创建RTSP服务器服务
    RTSPServerService server;
    
    // 初始化服务器
    if (!server.init(config)) {
        std::cerr << "Failed to initialize RTSP server" << std::endl;
        return -1;
    }
    
    std::cout << "RTSP server initialized successfully" << std::endl;
    
    // 启动服务器
    if (!server.start()) {
        std::cerr << "Failed to start RTSP server" << std::endl;
        return -1;
    }
    
    std::cout << "RTSP server started on rtsp://localhost:8554/test" << std::endl;
    std::cout << "You can test with: ffplay rtsp://localhost:8554/test" << std::endl;
    std::cout << "Press Ctrl+C to stop..." << std::endl;
    
    // 运行10秒钟进行测试
    for (int i = 0; i < 10; ++i) {
        std::this_thread::sleep_for(std::chrono::seconds(1));
        
        // 获取统计信息
        auto stats = server.get_stats();
        std::cout << "Stats - Uptime: " << stats.uptime_seconds << "s, "
                  << "Connections: " << stats.active_connections << "/" << stats.total_connections
                  << ", Frames: " << stats.frames_served << std::endl;
    }
    
    // 停止服务器
    server.stop();
    std::cout << "RTSP server stopped" << std::endl;
    
    return 0;
}
