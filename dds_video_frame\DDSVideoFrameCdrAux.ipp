// Copyright 2016 Proyectos y Sistemas de Mantenimiento SL (eProsima).
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

/*!
 * @file DDSVideoFrameCdrAux.ipp
 * This source file contains some declarations of CDR related functions.
 *
 * This file was generated by the tool fastddsgen.
 */

#ifndef FAST_DDS_GENERATED__DDSVIDEOFRAMECDRAUX_IPP
#define FAST_DDS_GENERATED__DDSVIDEOFRAMECDRAUX_IPP

#include "DDSVideoFrameCdrAux.hpp"

#include <fastcdr/Cdr.h>
#include <fastcdr/CdrSizeCalculator.hpp>


#include <fastcdr/exceptions/BadParamException.h>
using namespace eprosima::fastcdr::exception;

namespace eprosima {
namespace fastcdr {

template<>
eProsima_user_DllExport size_t calculate_serialized_size(
        eprosima::fastcdr::CdrSizeCalculator& calculator,
        const DDSVideoFrame& data,
        size_t& current_alignment)
{
    static_cast<void>(data);

    eprosima::fastcdr::EncodingAlgorithmFlag previous_encoding = calculator.get_encoding();
    size_t calculated_size {calculator.begin_calculate_type_serialized_size(
                                eprosima::fastcdr::CdrVersion::XCDRv2 == calculator.get_cdr_version() ?
                                eprosima::fastcdr::EncodingAlgorithmFlag::DELIMIT_CDR2 :
                                eprosima::fastcdr::EncodingAlgorithmFlag::PLAIN_CDR,
                                current_alignment)};


        calculated_size += calculator.calculate_member_serialized_size(eprosima::fastcdr::MemberId(0),
                data.frame_id(), current_alignment);

        calculated_size += calculator.calculate_member_serialized_size(eprosima::fastcdr::MemberId(1),
                data.timestamp(), current_alignment);

        calculated_size += calculator.calculate_member_serialized_size(eprosima::fastcdr::MemberId(2),
                data.width(), current_alignment);

        calculated_size += calculator.calculate_member_serialized_size(eprosima::fastcdr::MemberId(3),
                data.height(), current_alignment);

        calculated_size += calculator.calculate_member_serialized_size(eprosima::fastcdr::MemberId(4),
                data.format(), current_alignment);

        calculated_size += calculator.calculate_member_serialized_size(eprosima::fastcdr::MemberId(5),
                data.dma_fd(), current_alignment);

        calculated_size += calculator.calculate_member_serialized_size(eprosima::fastcdr::MemberId(6),
                data.source_type(), current_alignment);

        calculated_size += calculator.calculate_member_serialized_size(eprosima::fastcdr::MemberId(7),
                data.data_length(), current_alignment);

        calculated_size += calculator.calculate_member_serialized_size(eprosima::fastcdr::MemberId(8),
                data.is_keyframe(), current_alignment);

        calculated_size += calculator.calculate_member_serialized_size(eprosima::fastcdr::MemberId(9),
                data.offset(), current_alignment);

        calculated_size += calculator.calculate_member_serialized_size(eprosima::fastcdr::MemberId(10),
                data.data(), current_alignment);


    calculated_size += calculator.end_calculate_type_serialized_size(previous_encoding, current_alignment);

    return calculated_size;
}

template<>
eProsima_user_DllExport void serialize(
        eprosima::fastcdr::Cdr& scdr,
        const DDSVideoFrame& data)
{
    eprosima::fastcdr::Cdr::state current_state(scdr);
    scdr.begin_serialize_type(current_state,
            eprosima::fastcdr::CdrVersion::XCDRv2 == scdr.get_cdr_version() ?
            eprosima::fastcdr::EncodingAlgorithmFlag::DELIMIT_CDR2 :
            eprosima::fastcdr::EncodingAlgorithmFlag::PLAIN_CDR);

    scdr
        << eprosima::fastcdr::MemberId(0) << data.frame_id()
        << eprosima::fastcdr::MemberId(1) << data.timestamp()
        << eprosima::fastcdr::MemberId(2) << data.width()
        << eprosima::fastcdr::MemberId(3) << data.height()
        << eprosima::fastcdr::MemberId(4) << data.format()
        << eprosima::fastcdr::MemberId(5) << data.dma_fd()
        << eprosima::fastcdr::MemberId(6) << data.source_type()
        << eprosima::fastcdr::MemberId(7) << data.data_length()
        << eprosima::fastcdr::MemberId(8) << data.is_keyframe()
        << eprosima::fastcdr::MemberId(9) << data.offset()
        << eprosima::fastcdr::MemberId(10) << data.data()
;
    scdr.end_serialize_type(current_state);
}

template<>
eProsima_user_DllExport void deserialize(
        eprosima::fastcdr::Cdr& cdr,
        DDSVideoFrame& data)
{
    cdr.deserialize_type(eprosima::fastcdr::CdrVersion::XCDRv2 == cdr.get_cdr_version() ?
            eprosima::fastcdr::EncodingAlgorithmFlag::DELIMIT_CDR2 :
            eprosima::fastcdr::EncodingAlgorithmFlag::PLAIN_CDR,
            [&data](eprosima::fastcdr::Cdr& dcdr, const eprosima::fastcdr::MemberId& mid) -> bool
            {
                bool ret_value = true;
                switch (mid.id)
                {
                                        case 0:
                                                dcdr >> data.frame_id();
                                            break;

                                        case 1:
                                                dcdr >> data.timestamp();
                                            break;

                                        case 2:
                                                dcdr >> data.width();
                                            break;

                                        case 3:
                                                dcdr >> data.height();
                                            break;

                                        case 4:
                                                dcdr >> data.format();
                                            break;

                                        case 5:
                                                dcdr >> data.dma_fd();
                                            break;

                                        case 6:
                                                dcdr >> data.source_type();
                                            break;

                                        case 7:
                                                dcdr >> data.data_length();
                                            break;

                                        case 8:
                                                dcdr >> data.is_keyframe();
                                            break;

                                        case 9:
                                                dcdr >> data.offset();
                                            break;

                                        case 10:
                                                dcdr >> data.data();
                                            break;

                    default:
                        ret_value = false;
                        break;
                }
                return ret_value;
            });
}

void serialize_key(
        eprosima::fastcdr::Cdr& scdr,
        const DDSVideoFrame& data)
{

    static_cast<void>(scdr);
    static_cast<void>(data);
                        scdr << data.frame_id();

                        scdr << data.timestamp();

                        scdr << data.width();

                        scdr << data.height();

                        scdr << data.format();

                        scdr << data.dma_fd();

                        scdr << data.source_type();

                        scdr << data.data_length();

                        scdr << data.is_keyframe();

                        scdr << data.offset();

                        scdr << data.data();

}



} // namespace fastcdr
} // namespace eprosima

#endif // FAST_DDS_GENERATED__DDSVIDEOFRAMECDRAUX_IPP

