<dds xmlns="http://www.eprosima.com/XMLSchemas/fastRTPS_Profiles">
    <!-- 通用参与者配置 -->
    <participant profile_name="embedded_participant">
        <rtps>
            <sendBuffers>
                <preallocated_number>4</preallocated_number>
                <dynamic>false</dynamic>
            </sendBuffers>
            <userTransports>
                <transport_id>SharedMemoryTransport</transport_id>
            </userTransports>
            <useBuiltinTransports>false</useBuiltinTransports>
            <builtin>
                <discovery_config>
                    <leaseDuration>
                        <sec>10</sec>
                    </leaseDuration>
                </discovery_config>
            </builtin>
        </rtps>
    </participant>

    <!-- AI帧数据写入器 -->
    <data_writer profile_name="ai_frame_writer" qos_name="HighThroughput">
        <qos>
            <reliability>
                <kind>BEST_EFFORT_RELIABILITY_QOS</kind>
            </reliability>
            <durability>
                <kind>VOLATILE_DURABILITY_QOS</kind>
            </durability>
            <history>
                <kind>KEEP_LAST_HISTORY_QOS</kind>
                <depth>2</depth>
            </history>
            <resource_limits>
                <max_samples>5</max_samples>
                <allocated_samples>3</allocated_samples>
            </resource_limits>
            <data_sharing>
                <kind>AUTOMATIC</kind>
                <shared_memory_directory>/tmp/dds_shm</shared_memory_directory>
            </data_sharing>
        </qos>
    </data_writer>

    <!-- 云端帧数据写入器 -->
    <data_writer profile_name="cloud_frame_writer" qos_name="ReliableStreaming">
        <qos>
            <reliability>
                <kind>RELIABLE_RELIABILITY_QOS</kind>
                <max_blocking_time>
                    <sec>0</sec>
                    <nanosec>20000000</nanosec> <!-- 20ms -->
                </max_blocking_time>
            </reliability>
            <history>
                <kind>KEEP_LAST_HISTORY_QOS</kind>
                <depth>10</depth> <!-- 关键帧缓冲 -->
            </history>
            <resource_limits>
                <max_samples>20</max_samples>
                <allocated_samples>10</allocated_samples>
            </resource_limits>
            <publish_mode>
                <kind>ASYNCHRONOUS_PUBLISH_MODE</kind>
            </publish_mode>
        </qos>
    </data_writer>
  </dds>

