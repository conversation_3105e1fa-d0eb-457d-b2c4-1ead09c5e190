#!/bin/bash

# RTSP服务器测试编译脚本

set -e

echo "=== RTSP服务器测试编译脚本 ==="

# 检查依赖
echo "检查依赖..."

# 检查GStreamer
if ! pkg-config --exists gstreamer-1.0; then
    echo "错误: 未找到GStreamer开发库"
    echo "请安装: sudo apt-get install libgstreamer1.0-dev"
    exit 1
fi

# 检查GStreamer RTSP Server
if ! pkg-config --exists gstreamer-rtsp-server-1.0; then
    echo "错误: 未找到GStreamer RTSP Server开发库"
    echo "请安装: sudo apt-get install libgstreamer-rtsp-server-1.0-dev"
    exit 1
fi

# 检查Fast-DDS
if ! pkg-config --exists fastrtps; then
    echo "警告: 未找到Fast-DDS，将使用模拟实现"
fi

echo "依赖检查完成"

# 编译参数
CXXFLAGS="-std=c++17 -Wall -Wextra -O2"
INCLUDES="-I./include"

# GStreamer库
GSTREAMER_CFLAGS=$(pkg-config --cflags gstreamer-1.0 gstreamer-app-1.0 gstreamer-rtsp-server-1.0)
GSTREAMER_LIBS=$(pkg-config --libs gstreamer-1.0 gstreamer-app-1.0 gstreamer-rtsp-server-1.0)

# Fast-DDS库（如果可用）
if pkg-config --exists fastrtps; then
    FASTDDS_CFLAGS=$(pkg-config --cflags fastrtps)
    FASTDDS_LIBS=$(pkg-config --libs fastrtps)
else
    FASTDDS_CFLAGS=""
    FASTDDS_LIBS=""
fi

# 其他库
OTHER_LIBS="-lpthread -lglib-2.0 -lgobject-2.0"

echo "编译RTSP服务器实现..."

# 编译RTSP服务器源文件
g++ $CXXFLAGS $INCLUDES $GSTREAMER_CFLAGS $FASTDDS_CFLAGS \
    -c src/rtsp_server.cpp -o rtsp_server.o

echo "编译测试程序..."

# 编译测试程序
g++ $CXXFLAGS $INCLUDES $GSTREAMER_CFLAGS $FASTDDS_CFLAGS \
    -c test_rtsp_simple.cpp -o test_rtsp_simple.o

echo "链接可执行文件..."

# 链接
g++ rtsp_server.o test_rtsp_simple.o \
    $GSTREAMER_LIBS $FASTDDS_LIBS $OTHER_LIBS \
    -o test_rtsp_simple

echo "编译完成！"
echo ""
echo "运行测试:"
echo "  ./test_rtsp_simple"
echo ""
echo "测试RTSP流:"
echo "  ffplay rtsp://localhost:8554/test"
echo "  vlc rtsp://localhost:8554/test"
echo ""

# 清理临时文件
rm -f *.o

echo "编译脚本执行完成"
