cmake_minimum_required(VERSION 3.16)
project(VideoService VERSION 1.0.0 LANGUAGES CXX)

# Set C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Build type
if(NOT CMAKE_BUILD_TYPE)
    set(CMAKE_BUILD_TYPE Release)
endif()

# Compiler flags
set(CMAKE_CXX_FLAGS_DEBUG "-g -O0 -DDEBUG")
set(CMAKE_CXX_FLAGS_RELEASE "-O3 -DNDEBUG")

# Add common compiler flags
add_compile_options(-Wall -Wextra -Wpedantic)

# Project directories
set(PROJECT_INCLUDE_DIR ${CMAKE_CURRENT_SOURCE_DIR}/include)
set(PROJECT_SOURCE_DIR ${CMAKE_CURRENT_SOURCE_DIR}/src)
set(PROJECT_CONFIG_DIR ${CMAKE_CURRENT_SOURCE_DIR}/config)
set(PROJECT_SCRIPTS_DIR ${CMAKE_CURRENT_SOURCE_DIR}/scripts)
set(PROJECT_DOCS_DIR ${CMAKE_CURRENT_SOURCE_DIR}/docs)
set(PROJECT_TEST_DIR ${CMAKE_CURRENT_SOURCE_DIR}/test)
set(PROJECT_INSTALL_DIR ${CMAKE_CURRENT_SOURCE_DIR}/install)


# Find required packages
find_package(PkgConfig REQUIRED)

# FFmpeg (for RTSP packet capture)
pkg_check_modules(FFMPEG REQUIRED
    libavformat>=58.0
    libavcodec>=58.0
    libavutil>=56.0
)

# GStreamer (for video converter encoding/decoding and RTSP server)
pkg_check_modules(GSTREAMER REQUIRED
    gstreamer-1.0>=1.16
    gstreamer-app-1.0
    gstreamer-video-1.0
    gstreamer-webrtc-1.0
    gstreamer-sdp-1.0
    gstreamer-rtsp-server-1.0
)

# JsonCpp (for configuration parsing)
pkg_check_modules(JSONCPP jsoncpp)
if(NOT JSONCPP_FOUND)
    find_package(PkgConfig QUIET)
    if(PkgConfig_FOUND)
        pkg_check_modules(JSONCPP jsoncpp)
    endif()
    if(NOT JSONCPP_FOUND)
        message(WARNING "JsonCpp not found, RTSP server config file support disabled")
    endif()
endif()

# Fast DDS
find_package(fastdds 3 REQUIRED)
find_package(fastcdr 2 REQUIRED)

# OpenCV (optional, for image processing)
find_package(OpenCV QUIET)
if(OpenCV_FOUND)
    add_definitions(-DHAVE_OPENCV)
endif()

# TensorRT (optional)
find_path(TENSORRT_INCLUDE_DIR NvInfer.h
    HINTS ${TENSORRT_ROOT} ${CUDA_TOOLKIT_ROOT_DIR}
    PATH_SUFFIXES include)
find_library(TENSORRT_LIBRARY nvinfer
    HINTS ${TENSORRT_ROOT} ${CUDA_TOOLKIT_ROOT_DIR}
    PATH_SUFFIXES lib lib64 lib/x64)

if(TENSORRT_INCLUDE_DIR AND TENSORRT_LIBRARY)
    set(TENSORRT_FOUND TRUE)
    add_definitions(-DHAVE_TENSORRT)
endif()

# ONNX Runtime (optional)
find_path(ONNXRUNTIME_INCLUDE_DIR onnxruntime_cxx_api.h
    HINTS ${ONNXRUNTIME_ROOT}
    PATH_SUFFIXES include)
find_library(ONNXRUNTIME_LIBRARY onnxruntime
    HINTS ${ONNXRUNTIME_ROOT}
    PATH_SUFFIXES lib lib64)

if(ONNXRUNTIME_INCLUDE_DIR AND ONNXRUNTIME_LIBRARY)
    set(ONNXRUNTIME_FOUND TRUE)
    add_definitions(-DHAVE_ONNXRUNTIME)
endif()

# RKNN Runtime (optional, for RKNN models)
find_library(RKNN_API_LIBRARY NAMES rknnrt PATHS /usr/lib)
if(RKNN_API_LIBRARY)
    set(RKNNRUNTIME_FOUND TRUE)
    add_definitions(-DHAVE_RKNNRUNTIME)
endif()

# Rockchip RGA (optional, for image processing)
find_library(RGA_LIBRARY NAMES rga)
if(RGA_LIBRARY)
    set(RGA_FOUND TRUE)
    add_definitions(-DHAVE_RGA)
endif()

# Rockchip MPP (optional, for video encoding/decoding)
find_library(RKMPP_LIBRARY NAMES rockchip_mpp)
if(RKMPP_LIBRARY)
    set(RKMPP_FOUND TRUE)
    add_definitions(-DHAVE_RKMPP)
endif()

# Include directories
include_directories(${PROJECT_INCLUDE_DIR})
include_directories(${FFMPEG_INCLUDE_DIRS})
include_directories(${GSTREAMER_INCLUDE_DIRS})

if(TENSORRT_FOUND)
    include_directories(${TENSORRT_INCLUDE_DIR})
endif()

if(ONNXRUNTIME_FOUND)
    include_directories(${ONNXRUNTIME_INCLUDE_DIR})
endif()

if(OpenCV_FOUND)
    include_directories(${OpenCV_INCLUDE_DIRS})
endif()

# Link directories
link_directories(${FFMPEG_LIBRARY_DIRS})
link_directories(${GSTREAMER_LIBRARY_DIRS})

# Include sub CMakeLists.txt files
add_subdirectory(dds_video_frame)

# Common libraries
set(COMMON_LIBS
    ${FFMPEG_LIBRARIES}
    ${GSTREAMER_LIBRARIES}
    ${RGA_LIBRARY}
    ${RKNN_API_LIBRARY}
    ${RKMPP_LIBRARY}
    DDSVideoFrame_lib_${PROJECT_NAME}
    fastdds
    fastcdr
    pthread
    dl
)

if(TENSORRT_FOUND)
    list(APPEND COMMON_LIBS ${TENSORRT_LIBRARY})
endif()

if(ONNXRUNTIME_FOUND)
    list(APPEND COMMON_LIBS ${ONNXRUNTIME_LIBRARY})
endif()

if(OpenCV_FOUND)
    list(APPEND COMMON_LIBS ${OpenCV_LIBS})
endif()


# Video Capture Service
add_executable(video_capture
    ${PROJECT_SOURCE_DIR}/video_capture_main.cpp
)
target_link_libraries(video_capture ${COMMON_LIBS})

# Video Converter Service
add_executable(video_converter
    ${PROJECT_SOURCE_DIR}/video_converter_main.cpp
)
target_link_libraries(video_converter ${COMMON_LIBS})

# AI Processor Service
add_executable(ai_processor
    ${PROJECT_SOURCE_DIR}/ai_processor_main.cpp
)
target_link_libraries(ai_processor ${COMMON_LIBS})

# Cloud Streamer Service
add_executable(cloud_streamer
    ${PROJECT_SOURCE_DIR}/cloud_streamer_main.cpp
)
target_link_libraries(cloud_streamer ${COMMON_LIBS})

# RTSP Server Service
add_executable(rtsp_server
    ${PROJECT_SOURCE_DIR}/rtsp_server_main.cpp
    ${PROJECT_SOURCE_DIR}/rtsp_server.cpp
)
target_link_libraries(rtsp_server ${COMMON_LIBS})
if(JSONCPP_FOUND)
    target_link_libraries(rtsp_server ${JSONCPP_LIBRARIES})
    target_include_directories(rtsp_server PRIVATE ${JSONCPP_INCLUDE_DIRS})
    target_compile_definitions(rtsp_server PRIVATE HAVE_JSONCPP)
endif()

# Testing support
option(BUILD_TESTS "Build tests" OFF)

if(BUILD_TESTS)
    # Find Google Test
    find_package(GTest QUIET)
    if(GTest_FOUND)
        enable_testing()

        # Basic tests
        add_executable(test_basic
            ${PROJECT_TEST_DIR}/test_basic.cpp
        )
        target_link_libraries(test_basic
            ${COMMON_LIBS}
            GTest::gtest
            GTest::gtest_main
            GTest::gmock
        )

        # Add test to CTest
        add_test(NAME BasicTests COMMAND test_basic)

        # Install test executable
        install(TARGETS test_basic DESTINATION bin)
    else()
        message(WARNING "Google Test not found. Tests will not be built.")
    endif()
endif()

# Install targets
install(TARGETS
    video_capture
    video_converter
    ai_processor
    cloud_streamer
    rtsp_server
    DESTINATION bin
)

# Install header files
install(DIRECTORY ${PROJECT_INCLUDE_DIR}/
    DESTINATION include/video_service
    FILES_MATCHING PATTERN "*.h"
)

# Install configuration files
install(FILES
    ${PROJECT_CONFIG_DIR}/config.json
    DESTINATION share/video_service
)

# Install scripts
install(DIRECTORY ${PROJECT_SCRIPTS_DIR}/
    DESTINATION share/video_service/scripts
    FILES_MATCHING PATTERN "*.sh"
    PERMISSIONS OWNER_READ OWNER_WRITE OWNER_EXECUTE
                GROUP_READ GROUP_EXECUTE
                WORLD_READ WORLD_EXECUTE
)

# Install documentation
install(FILES
    ${PROJECT_DOCS_DIR}/README.md
    DESTINATION share/doc/video_service
)

# Install systemd service files (if systemd is available)
if(EXISTS "/lib/systemd/system")
    configure_file(
        ${PROJECT_CONFIG_DIR}/video_service.service.in
        ${CMAKE_CURRENT_BINARY_DIR}/video_service.service
        @ONLY
    )
    install(FILES
        ${CMAKE_CURRENT_BINARY_DIR}/video_service.service
        DESTINATION /lib/systemd/system
    )
endif()

# Create package
set(CPACK_PACKAGE_NAME "video-service")
set(CPACK_PACKAGE_VERSION ${PROJECT_VERSION})
set(CPACK_PACKAGE_DESCRIPTION_SUMMARY "Embedded Linux Video Service System")
set(CPACK_PACKAGE_VENDOR "Video Service Team")
set(CPACK_PACKAGE_CONTACT "<EMAIL>")

# DEB package specific
set(CPACK_DEBIAN_PACKAGE_DEPENDS
    "libavformat58, libavcodec58, libavutil56, libgstreamer1.0-0 (>= 1.16), libgstreamer-plugins-base1.0-0")
set(CPACK_DEBIAN_PACKAGE_SECTION "video")
set(CPACK_DEBIAN_PACKAGE_PRIORITY "optional")

# RPM package specific
set(CPACK_RPM_PACKAGE_REQUIRES
    "ffmpeg-libs >= 4.0, gstreamer1 >= 1.16, gstreamer1-plugins-base")
set(CPACK_RPM_PACKAGE_GROUP "Applications/Multimedia")

include(CPack)

# Add test subdirectory
add_subdirectory(test)

# Print configuration summary
message(STATUS "")
message(STATUS "Video Service Configuration Summary:")
message(STATUS "  Build type: ${CMAKE_BUILD_TYPE}")
message(STATUS "  C++ standard: ${CMAKE_CXX_STANDARD}")
message(STATUS "  FFmpeg found: ${FFMPEG_FOUND}")
message(STATUS "  GStreamer found: ${GSTREAMER_FOUND}")
message(STATUS "  OpenCV found: ${OpenCV_FOUND}")
message(STATUS "  Fast DDS found: ${fastdds_FOUND}")
message(STATUS "  Fast CDR found: ${fastcdr_FOUND}")
message(STATUS "  TensorRT found: ${TENSORRT_FOUND}")
message(STATUS "  ONNX Runtime found: ${ONNXRUNTIME_FOUND}")
message(STATUS "  RKNN Runtime found: ${RKNNRUNTIME_FOUND}")
message(STATUS "  Rockchip RGA found: ${RGA_FOUND}")
message(STATUS "  Rockchip MPP found: ${RKMPP_FOUND}")
message(STATUS "")
