cmake_minimum_required(VERSION 3.16)
project(VideoService VERSION 1.0.0 LANGUAGES CXX)

# Set C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Build type
if(NOT CMAKE_BUILD_TYPE)
    set(CMAKE_BUILD_TYPE Release)
endif()

# Compiler flags
set(CMAKE_CXX_FLAGS_DEBUG "-g -O0 -DDEBUG")
set(CMAKE_CXX_FLAGS_RELEASE "-O3 -DNDEBUG")

# Add common compiler flags
add_compile_options(-Wall -Wextra -Wpedantic)

# Find required packages
find_package(PkgConfig REQUIRED)

# FFmpeg
pkg_check_modules(FFMPEG REQUIRED
    libavcodec
    libavformat
    libavutil
    libswscale
    libswresample
)

# GStreamer
pkg_check_modules(GSTREAMER REQUIRED
    gstreamer-1.0
    gstreamer-app-1.0
    gstreamer-video-1.0
    gstreamer-webrtc-1.0
)

# Fast DDS
find_package(fastcdr REQUIRED)
find_package(fastrtps REQUIRED)

# OpenCV (optional, for image processing)
find_package(OpenCV QUIET)
if(OpenCV_FOUND)
    add_definitions(-DHAVE_OPENCV)
endif()

# TensorRT (optional)
find_path(TENSORRT_INCLUDE_DIR NvInfer.h
    HINTS ${TENSORRT_ROOT} ${CUDA_TOOLKIT_ROOT_DIR}
    PATH_SUFFIXES include)
find_library(TENSORRT_LIBRARY nvinfer
    HINTS ${TENSORRT_ROOT} ${CUDA_TOOLKIT_ROOT_DIR}
    PATH_SUFFIXES lib lib64 lib/x64)

if(TENSORRT_INCLUDE_DIR AND TENSORRT_LIBRARY)
    set(TENSORRT_FOUND TRUE)
    add_definitions(-DHAVE_TENSORRT)
endif()

# ONNX Runtime (optional)
find_path(ONNXRUNTIME_INCLUDE_DIR onnxruntime_cxx_api.h
    HINTS ${ONNXRUNTIME_ROOT}
    PATH_SUFFIXES include)
find_library(ONNXRUNTIME_LIBRARY onnxruntime
    HINTS ${ONNXRUNTIME_ROOT}
    PATH_SUFFIXES lib lib64)

if(ONNXRUNTIME_INCLUDE_DIR AND ONNXRUNTIME_LIBRARY)
    set(ONNXRUNTIME_FOUND TRUE)
    add_definitions(-DHAVE_ONNXRUNTIME)
endif()

# Include directories
include_directories(${CMAKE_CURRENT_SOURCE_DIR})
include_directories(${FFMPEG_INCLUDE_DIRS})
include_directories(${GSTREAMER_INCLUDE_DIRS})

if(TENSORRT_FOUND)
    include_directories(${TENSORRT_INCLUDE_DIR})
endif()

if(ONNXRUNTIME_FOUND)
    include_directories(${ONNXRUNTIME_INCLUDE_DIR})
endif()

if(OpenCV_FOUND)
    include_directories(${OpenCV_INCLUDE_DIRS})
endif()

# Link directories
link_directories(${FFMPEG_LIBRARY_DIRS})
link_directories(${GSTREAMER_LIBRARY_DIRS})

# Common libraries
set(COMMON_LIBS
    ${FFMPEG_LIBRARIES}
    ${GSTREAMER_LIBRARIES}
    fastrtps
    fastcdr
    pthread
    dl
)

if(TENSORRT_FOUND)
    list(APPEND COMMON_LIBS ${TENSORRT_LIBRARY})
endif()

if(ONNXRUNTIME_FOUND)
    list(APPEND COMMON_LIBS ${ONNXRUNTIME_LIBRARY})
endif()

if(OpenCV_FOUND)
    list(APPEND COMMON_LIBS ${OpenCV_LIBS})
endif()

# Video Capture Service
add_executable(video_capture_main
    video_capture_main.cpp
)
target_link_libraries(video_capture_main ${COMMON_LIBS})

# Video Converter Service
add_executable(video_converter_main
    video_converter_main.cpp
)
target_link_libraries(video_converter_main ${COMMON_LIBS})

# AI Processor Service
add_executable(ai_processor_main
    ai_processor_main.cpp
)
target_link_libraries(ai_processor_main ${COMMON_LIBS})

# Cloud Streamer Service
add_executable(cloud_streamer_main
    cloud_streamer_main.cpp
)
target_link_libraries(cloud_streamer_main ${COMMON_LIBS})

# Install targets
install(TARGETS 
    video_capture_main
    video_converter_main
    ai_processor_main
    cloud_streamer_main
    DESTINATION bin
)

# Install configuration files
install(FILES
    config.json
    video_service_manager.sh
    DESTINATION share/video_service
)

# Install systemd service files (if systemd is available)
if(EXISTS "/lib/systemd/system")
    configure_file(
        ${CMAKE_CURRENT_SOURCE_DIR}/video_service.service.in
        ${CMAKE_CURRENT_BINARY_DIR}/video_service.service
        @ONLY
    )
    install(FILES
        ${CMAKE_CURRENT_BINARY_DIR}/video_service.service
        DESTINATION /lib/systemd/system
    )
endif()

# Create package
set(CPACK_PACKAGE_NAME "video-service")
set(CPACK_PACKAGE_VERSION ${PROJECT_VERSION})
set(CPACK_PACKAGE_DESCRIPTION_SUMMARY "Embedded Linux Video Service System")
set(CPACK_PACKAGE_VENDOR "Video Service Team")
set(CPACK_PACKAGE_CONTACT "<EMAIL>")

# DEB package specific
set(CPACK_DEBIAN_PACKAGE_DEPENDS 
    "libavcodec58, libavformat58, libavutil56, libswscale5, libswresample3, libgstreamer1.0-0, libgstreamer-plugins-base1.0-0")
set(CPACK_DEBIAN_PACKAGE_SECTION "video")
set(CPACK_DEBIAN_PACKAGE_PRIORITY "optional")

# RPM package specific
set(CPACK_RPM_PACKAGE_REQUIRES 
    "ffmpeg-libs, gstreamer1, gstreamer1-plugins-base")
set(CPACK_RPM_PACKAGE_GROUP "Applications/Multimedia")

include(CPack)

# Print configuration summary
message(STATUS "")
message(STATUS "Video Service Configuration Summary:")
message(STATUS "  Build type: ${CMAKE_BUILD_TYPE}")
message(STATUS "  C++ standard: ${CMAKE_CXX_STANDARD}")
message(STATUS "  FFmpeg found: ${FFMPEG_FOUND}")
message(STATUS "  GStreamer found: ${GSTREAMER_FOUND}")
message(STATUS "  OpenCV found: ${OpenCV_FOUND}")
message(STATUS "  TensorRT found: ${TENSORRT_FOUND}")
message(STATUS "  ONNX Runtime found: ${ONNXRUNTIME_FOUND}")
message(STATUS "")
