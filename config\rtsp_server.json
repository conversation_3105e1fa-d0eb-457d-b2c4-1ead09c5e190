{"rtsp_server": {"dds_topic": "Video_Frames", "server_address": "0.0.0.0", "server_port": 8554, "mount_point": "/stream", "output_video": {"width": 1280, "height": 720, "fps": 30, "codec": "H264", "bitrate": 2000000, "gop_size": 30}, "encoder": {"use_hardware_encoder": true, "fallback_to_software": true, "preset": "low-latency", "rate_control": "CBR"}, "performance": {"zero_copy_mode": true, "buffer_size": 5, "max_clients": 10, "thread_priority": 80}, "quality_control": {"adaptive_bitrate": true, "min_bitrate": 500000, "max_bitrate": 5000000, "quality_levels": [{"name": "low", "width": 640, "height": 360, "bitrate": 500000}, {"name": "medium", "width": 1280, "height": 720, "bitrate": 2000000}, {"name": "high", "width": 1920, "height": 1080, "bitrate": 5000000}]}, "transport": {"rtp_port_range": {"min": 50000, "max": 60000}, "rtcp_port_range": {"min": 60001, "max": 65000}, "multicast": {"enabled": false, "address": "*********", "port": 5000, "ttl": 1}}, "logging": {"level": "INFO", "stats_interval": 10, "performance_monitoring": true}}, "multiple_streams": {"enabled": false, "streams": [{"name": "main_stream", "dds_topic": "Video_Frames", "mount_point": "/main", "width": 1280, "height": 720, "bitrate": 2000000}, {"name": "ai_stream", "dds_topic": "AI_Frames", "mount_point": "/ai", "width": 640, "height": 640, "bitrate": 1000000}, {"name": "cloud_stream", "dds_topic": "Cloud_Frames", "mount_point": "/cloud", "width": 1920, "height": 1080, "bitrate": 5000000}]}, "authentication": {"enabled": false, "method": "basic", "users": [{"username": "admin", "password": "password123"}, {"username": "viewer", "password": "view123"}]}, "recording": {"enabled": false, "output_directory": "/var/recordings", "filename_format": "stream_%Y%m%d_%H%M%S.mp4", "max_file_size_mb": 1000, "max_duration_minutes": 60, "cleanup_old_files": true, "retention_days": 7}}