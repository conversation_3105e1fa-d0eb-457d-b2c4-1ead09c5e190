#include "video_capture.h"
#include "common.h"
#include <signal.h>
#include <getopt.h>
#include <iostream>

// 全局变量
std::unique_ptr<VideoCaptureService> g_capture_service;

// 信号处理函数
void signal_handler(int signal) {
    switch (signal) {
        case SIGINT:
        case SIGTERM:
            LOG_I("Received termination signal, stopping...");
            if (g_capture_service) {
                g_capture_service->stop();
            }
            break;
        case SIGUSR1:
        case SIGUSR2:
            if (g_capture_service) {
                g_capture_service->handle_signal(signal);
            }
            break;
        default:
            break;
    }
}

void print_usage(const char* program_name) {
    std::cout << "Usage: " << program_name << " [OPTIONS]\n"
              << "Options:\n"
              << "  -s, --source TYPE     Video source type (v4l2|rtsp)\n"
              << "  -d, --device PATH     V4L2 device path (default: /dev/video0)\n"
              << "  -u, --url URL         RTSP URL\n"
              << "  -w, --width WIDTH     Video width (default: 1280)\n"
              << "  -h, --height HEIGHT   Video height (default: 720)\n"
              << "  -f, --fps FPS         Frame rate (default: 30)\n"
              << "  -t, --tcp             Use TCP for RTSP (default: UDP)\n"
              << "  --no-dma              Disable DMA buffers\n"
              << "  --help                Show this help message\n";
}

int main(int argc, char* argv[]) {
    // 设置日志级别
    Logger::set_level(LOG_INFO);
    
    // 默认配置
    CaptureConfig config;
    config.source_type = V4L2_SOURCE;
    config.device = "/dev/video0";
    config.width = 1280;
    config.height = 720;
    config.fps = 30;
    config.use_tcp = false;
    config.use_dma = true;
    
    // 解析命令行参数
    static struct option long_options[] = {
        {"source", required_argument, 0, 's'},
        {"device", required_argument, 0, 'd'},
        {"url", required_argument, 0, 'u'},
        {"width", required_argument, 0, 'w'},
        {"height", required_argument, 0, 'h'},
        {"fps", required_argument, 0, 'f'},
        {"tcp", no_argument, 0, 't'},
        {"no-dma", no_argument, 0, 1},
        {"help", no_argument, 0, 2},
        {0, 0, 0, 0}
    };
    
    int c;
    while ((c = getopt_long(argc, argv, "s:d:u:w:h:f:t", long_options, nullptr)) != -1) {
        switch (c) {
            case 's':
                if (strcmp(optarg, "v4l2") == 0) {
                    config.source_type = V4L2_SOURCE;
                } else if (strcmp(optarg, "rtsp") == 0) {
                    config.source_type = RTSP_SOURCE;
                } else {
                    std::cerr << "Invalid source type: " << optarg << std::endl;
                    return 1;
                }
                break;
            case 'd':
                config.device = optarg;
                break;
            case 'u':
                config.url = optarg;
                break;
            case 'w':
                config.width = atoi(optarg);
                break;
            case 'h':
                config.height = atoi(optarg);
                break;
            case 'f':
                config.fps = atoi(optarg);
                break;
            case 't':
                config.use_tcp = true;
                break;
            case 1:
                config.use_dma = false;
                break;
            case 2:
                print_usage(argv[0]);
                return 0;
            default:
                print_usage(argv[0]);
                return 1;
        }
    }
    
    // 验证配置
    if (config.source_type == RTSP_SOURCE && config.url.empty()) {
        std::cerr << "RTSP URL is required for RTSP source" << std::endl;
        return 1;
    }
    
    // 设置信号处理
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);
    signal(SIGUSR1, signal_handler);
    signal(SIGUSR2, signal_handler);
    
    LOG_I("Starting video capture service...");
    LOG_I("Source: %s", (config.source_type == V4L2_SOURCE) ? "V4L2" : "RTSP");
    if (config.source_type == V4L2_SOURCE) {
        LOG_I("Device: %s, Resolution: %dx%d, FPS: %d, DMA: %s",
              config.device.c_str(), config.width, config.height, config.fps,
              config.use_dma ? "enabled" : "disabled");
    } else {
        LOG_I("URL: %s, TCP: %s", config.url.c_str(), config.use_tcp ? "yes" : "no");
    }
    
    try {
        // 创建并初始化服务
        g_capture_service = std::make_unique<VideoCaptureService>();
        if (!g_capture_service->init(config)) {
            LOG_E("Failed to initialize video capture service");
            return 1;
        }
        
        // 启动服务
        g_capture_service->start();
        
        // 主循环 - 定期输出统计信息
        while (true) {
            VideoCaptureService::Stats stats;
            std::this_thread::sleep_for(std::chrono::seconds(10));
            g_capture_service->get_stats(stats);
            LOG_I("Stats - Captured: %lu, Dropped: %lu, FPS: %.1f, CPU: %.1f%%",
                  stats.frames_captured, stats.frames_dropped, stats.fps, stats.cpu_usage);
        }
        
    } catch (const std::exception& e) {
        LOG_E("Exception in main: %s", e.what());
        return 1;
    }
    
    LOG_I("Video capture service stopped");
    return 0;
}
